<?php
// XAMPP Email Setup Guide and Configuration
require 'db_connect.php';

$message = '';
$messageType = '';

// Handle configuration update
if ($_POST && isset($_POST['update_config'])) {
    $gmail_username = trim($_POST['gmail_username']);
    $gmail_password = trim($_POST['gmail_password']);
    $from_name = trim($_POST['from_name']);
    
    if (empty($gmail_username) || empty($gmail_password)) {
        $message = 'Please fill in all Gmail credentials.';
        $messageType = 'error';
    } elseif (!filter_var($gmail_username, FILTER_VALIDATE_EMAIL)) {
        $message = 'Please enter a valid Gmail address.';
        $messageType = 'error';
    } else {
        // Update email_config.php
        $config_content = file_get_contents('email_config.php');
        
        $config_content = str_replace(
            "define('SMTP_USERNAME', '<EMAIL>');",
            "define('SMTP_USERNAME', '$gmail_username');",
            $config_content
        );
        
        $config_content = str_replace(
            "define('SMTP_PASSWORD', 'your-app-password');",
            "define('SMTP_PASSWORD', '$gmail_password');",
            $config_content
        );
        
        $config_content = str_replace(
            "define('SMTP_FROM_EMAIL', '<EMAIL>');",
            "define('SMTP_FROM_EMAIL', '$gmail_username');",
            $config_content
        );
        
        if (!empty($from_name)) {
            $config_content = str_replace(
                "define('SMTP_FROM_NAME', 'Disaster Watch System');",
                "define('SMTP_FROM_NAME', '$from_name');",
                $config_content
            );
        }
        
        if (file_put_contents('email_config.php', $config_content)) {
            $message = 'Configuration updated successfully! You can now send emails.';
            $messageType = 'success';
        } else {
            $message = 'Failed to update configuration file. Check file permissions.';
            $messageType = 'error';
        }
    }
}

// Test email functionality
if ($_POST && isset($_POST['test_email'])) {
    require 'email_config.php';
    require 'simple_mailer.php';
    
    $test_email = trim($_POST['test_email_address']);
    
    if (empty($test_email)) {
        $message = 'Please enter a test email address.';
        $messageType = 'error';
    } elseif (!filter_var($test_email, FILTER_VALIDATE_EMAIL)) {
        $message = 'Please enter a valid email address.';
        $messageType = 'error';
    } else {
        $result = sendEmailSimple(
            $test_email,
            'Test Email from Disaster Watch System',
            "This is a test email to verify that your email configuration is working correctly.\n\nIf you receive this email, your XAMPP email system is properly configured!\n\nSent at: " . date('Y-m-d H:i:s'),
            'XAMPP Email Test'
        );
        
        if ($result['success']) {
            $message = 'Test email sent successfully to ' . htmlspecialchars($test_email) . '!';
            $messageType = 'success';
        } else {
            $message = 'Test email failed: ' . $result['message'];
            $messageType = 'error';
        }
    }
}

// Check current configuration
$config_status = 'not_configured';
if (file_exists('email_config.php')) {
    $config_content = file_get_contents('email_config.php');
    if (strpos($config_content, '<EMAIL>') === false && 
        strpos($config_content, 'your-app-password') === false) {
        $config_status = 'configured';
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>XAMPP Email Setup - Disaster Watch</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        .setup-container { margin-top: 20px; }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .step-card {
            margin-bottom: 20px;
        }
        .step-number {
            background-color: #2196f3;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }
        .configured {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
    </style>
</head>
<body>
    <nav class="blue darken-2">
        <div class="nav-wrapper">
            <span style="margin-left:14.9px; font-size:17.6px;">
                <i class="material-icons left">settings</i>XAMPP Email Setup
            </span>
            <a href="send_email_advanced.php" class="right btn transparent z-depth-0">
                <i class="material-icons left">email</i>Go to Email System
            </a>
        </div>
    </nav>

    <div class="container setup-container">
        <div class="row">
            <div class="col s12">
                <h4>Email System Setup for XAMPP</h4>
                <p>XAMPP doesn't include a mail server by default. This setup will configure Gmail SMTP for sending emails.</p>
                
                <?php if ($message): ?>
                    <div class="message <?php echo $messageType; ?>">
                        <i class="material-icons left"><?php echo $messageType === 'success' ? 'check_circle' : 'error'; ?></i>
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <!-- Step 1: Gmail Setup -->
                <div class="card step-card">
                    <div class="card-content">
                        <span class="card-title">
                            <span class="step-number">1</span>Setup Gmail App Password
                        </span>
                        <div class="row">
                            <div class="col s12 m8">
                                <ol>
                                    <li>Go to your <a href="https://myaccount.google.com/" target="_blank">Google Account</a></li>
                                    <li>Click on <strong>Security</strong> in the left sidebar</li>
                                    <li>Under "Signing in to Google", enable <strong>2-Step Verification</strong></li>
                                    <li>Once 2-Step Verification is enabled, click on <strong>App passwords</strong></li>
                                    <li>Select "Mail" as the app and generate a password</li>
                                    <li>Copy the 16-character app password (it will look like: abcd efgh ijkl mnop)</li>
                                </ol>
                            </div>
                            <div class="col s12 m4">
                                <div class="card-panel orange lighten-4">
                                    <i class="material-icons left">info</i>
                                    <strong>Important:</strong> Use the App Password, not your regular Gmail password!
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Configuration -->
                <div class="card step-card <?php echo $config_status === 'configured' ? 'configured' : ''; ?>">
                    <div class="card-content">
                        <span class="card-title">
                            <span class="step-number">2</span>Configure Email Settings
                            <?php if ($config_status === 'configured'): ?>
                                <span class="chip green white-text right">Configured</span>
                            <?php endif; ?>
                        </span>
                        
                        <form method="POST">
                            <div class="row">
                                <div class="input-field col s12 m6">
                                    <input type="email" id="gmail_username" name="gmail_username" required>
                                    <label for="gmail_username">Gmail Address</label>
                                </div>
                                <div class="input-field col s12 m6">
                                    <input type="text" id="gmail_password" name="gmail_password" required>
                                    <label for="gmail_password">Gmail App Password</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="input-field col s12">
                                    <input type="text" id="from_name" name="from_name" value="Disaster Watch System">
                                    <label for="from_name">Sender Name (optional)</label>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col s12">
                                    <button type="submit" name="update_config" class="btn blue darken-2 waves-effect waves-light">
                                        <i class="material-icons left">save</i>Save Configuration
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Step 3: Test Email -->
                <?php if ($config_status === 'configured'): ?>
                <div class="card step-card">
                    <div class="card-content">
                        <span class="card-title">
                            <span class="step-number">3</span>Test Email Sending
                        </span>
                        
                        <form method="POST">
                            <div class="row">
                                <div class="input-field col s12 m8">
                                    <input type="email" id="test_email_address" name="test_email_address" required>
                                    <label for="test_email_address">Test Email Address</label>
                                </div>
                                <div class="col s12 m4">
                                    <button type="submit" name="test_email" class="btn green waves-effect waves-light" style="margin-top: 25px;">
                                        <i class="material-icons left">send</i>Send Test Email
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Next Steps -->
                <div class="card">
                    <div class="card-content">
                        <span class="card-title">
                            <i class="material-icons left">check_circle</i>Next Steps
                        </span>
                        <ul class="collection">
                            <li class="collection-item">
                                <i class="material-icons left">email</i>
                                <a href="send_email_advanced.php">Use the Advanced Email System</a>
                                <span class="secondary-content">Send emails with templates</span>
                            </li>
                            <li class="collection-item">
                                <i class="material-icons left">mail_outline</i>
                                <a href="send_email.php">Use the Basic Email System</a>
                                <span class="secondary-content">Simple email sending</span>
                            </li>
                            <li class="collection-item">
                                <i class="material-icons left">dashboard</i>
                                <a href="index.php">Return to Dashboard</a>
                                <span class="secondary-content">Main system dashboard</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Troubleshooting -->
                <div class="card">
                    <div class="card-content">
                        <span class="card-title">
                            <i class="material-icons left">help</i>Troubleshooting
                        </span>
                        <div class="row">
                            <div class="col s12 m6">
                                <h6>Common Issues:</h6>
                                <ul>
                                    <li><strong>Authentication failed:</strong> Check your app password</li>
                                    <li><strong>Connection timeout:</strong> Check your internet connection</li>
                                    <li><strong>Permission denied:</strong> Enable "Less secure app access" (not recommended)</li>
                                </ul>
                            </div>
                            <div class="col s12 m6">
                                <h6>Gmail Requirements:</h6>
                                <ul>
                                    <li>2-Factor Authentication must be enabled</li>
                                    <li>Use App Password, not regular password</li>
                                    <li>App Password should be 16 characters</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
        });
    </script>
</body>
</html>
