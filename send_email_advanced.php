<?php
session_start();
require 'db_connect.php';
require 'email_config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

$message = '';
$messageType = '';

// Check email configuration
$config_check = validateEmailConfig();
if (!$config_check['status']) {
    $message = 'Email Configuration Error: ' . $config_check['message'];
    $messageType = 'error';
}

// Function to send email using PHP mail or SMTP
function sendEmail($to, $subject, $body, $from_name = '') {
    global $conn;
    
    if (!EMAIL_ENABLED) {
        return ['success' => false, 'message' => 'Email sending is disabled'];
    }
    
    $from_name = $from_name ?: SMTP_FROM_NAME;
    
    if (USE_SMTP) {
        // Use PHPMailer for SMTP (if available)
        if (class_exists('<PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer')) {
            return sendEmailSMTP($to, $subject, $body, $from_name);
        } else {
            return ['success' => false, 'message' => 'PHPMailer not installed. Please install it or use PHP mail()'];
        }
    } else {
        // Use PHP mail function
        return sendEmailPHP($to, $subject, $body, $from_name);
    }
}

// Function to send email using PHP mail()
function sendEmailPHP($to, $subject, $body, $from_name) {
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: $from_name <" . SMTP_FROM_EMAIL . ">" . "\r\n";
    $headers .= "Reply-To: " . SMTP_FROM_EMAIL . "\r\n";
    
    $html_body = formatEmailBody($subject, $body, $from_name);
    
    if (mail($to, $subject, $html_body, $headers)) {
        return ['success' => true, 'message' => 'Email sent successfully'];
    } else {
        return ['success' => false, 'message' => 'Failed to send email using PHP mail()'];
    }
}

// Function to send email using SMTP (PHPMailer)
function sendEmailSMTP($to, $subject, $body, $from_name) {
    try {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = SMTP_AUTH;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = SMTP_SECURE;
        $mail->Port = SMTP_PORT;
        
        // Recipients
        $mail->setFrom(SMTP_FROM_EMAIL, $from_name);
        $mail->addAddress($to);
        $mail->addReplyTo(SMTP_FROM_EMAIL, $from_name);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = formatEmailBody($subject, $body, $from_name);
        $mail->AltBody = strip_tags($body);
        
        $mail->send();
        return ['success' => true, 'message' => 'Email sent successfully via SMTP'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'SMTP Error: ' . $mail->ErrorInfo];
    }
}

// Function to format email body as HTML
function formatEmailBody($subject, $body, $from_name) {
    return "
    <html>
    <head>
        <title>$subject</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #1976d2; color: white; padding: 20px; text-align: center; }
            .content { background-color: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0; }
            .footer { color: #666; font-size: 12px; text-align: center; margin-top: 20px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>$subject</h2>
            </div>
            <div class='content'>
                " . nl2br(htmlspecialchars($body)) . "
            </div>
            <div class='footer'>
                <hr>
                <p>
                    This email was sent from the Disaster Watch System.<br>
                    Sent by: " . htmlspecialchars($from_name) . "<br>
                    Date: " . date('Y-m-d H:i:s') . "
                </p>
            </div>
        </div>
    </body>
    </html>";
}

// Handle form submission
if ($_POST && isset($_POST['send_email'])) {
    $to_email = trim($_POST['to_email']);
    $subject = trim($_POST['subject']);
    $email_body = trim($_POST['email_body']);
    $from_name = trim($_POST['from_name']);
    $template_used = $_POST['template_used'] ?? '';
    
    // Validate inputs
    if (empty($to_email) || empty($subject) || empty($email_body)) {
        $message = 'Please fill in all required fields.';
        $messageType = 'error';
    } elseif (!filter_var($to_email, FILTER_VALIDATE_EMAIL)) {
        $message = 'Please enter a valid email address.';
        $messageType = 'error';
    } else {
        // Send email
        $result = sendEmail($to_email, $subject, $email_body, $from_name);
        
        if ($result['success']) {
            // Log successful email
            $stmt = $conn->prepare("INSERT INTO email_logs (user_id, to_email, subject, body, sent_at, status, template_used) VALUES (?, ?, ?, ?, NOW(), 'sent', ?)");
            $stmt->bind_param("issss", $_SESSION['user_id'], $to_email, $subject, $email_body, $template_used);
            $stmt->execute();
            
            $message = $result['message'] . ' to ' . htmlspecialchars($to_email);
            $messageType = 'success';
            
            // Clear form data on success
            $_POST = array();
        } else {
            // Log failed attempt
            $stmt = $conn->prepare("INSERT INTO email_logs (user_id, to_email, subject, body, sent_at, status, template_used, error_message) VALUES (?, ?, ?, ?, NOW(), 'failed', ?, ?)");
            $stmt->bind_param("isssss", $_SESSION['user_id'], $to_email, $subject, $email_body, $template_used, $result['message']);
            $stmt->execute();
            
            $message = $result['message'];
            $messageType = 'error';
        }
    }
}

// Handle template loading via AJAX
if (isset($_GET['get_template']) && isset($_GET['template_name'])) {
    $template = getEmailTemplate($_GET['template_name']);
    if ($template) {
        header('Content-Type: application/json');
        echo json_encode($template);
        exit;
    }
}

// Get recent email logs for current user
$email_logs = [];
$stmt = $conn->prepare("SELECT * FROM email_logs WHERE user_id = ? ORDER BY sent_at DESC LIMIT 15");
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
while ($row = $result->fetch_assoc()) {
    $email_logs[] = $row;
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Send Email to Gmail - Disaster Watch</title>
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        .email-form { margin-top: 20px; }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .template-card {
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 10px;
        }
        .template-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .log-item {
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
            background-color: #f5f5f5;
            border-radius: 0 4px 4px 0;
        }
        .log-item.failed { border-left-color: #f44336; }
        .log-item.sent { border-left-color: #4caf50; }
        .config-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <?php include 'header.php'; ?>
    
    <nav class="blue darken-2 z-depth-0">
        <div class="nav-wrapper">
            <span style="margin-left:14.9px; font-size:17.6px;">
                <i class="material-icons left">email</i>Send Email to Gmail
            </span>
            <a href="index.php" class="right btn transparent z-depth-0">
                <i class="material-icons left">arrow_back</i>Back to Dashboard
            </a>
        </div>
    </nav>

    <div class="container">
        <div class="row">
            <div class="col s12">
                
                <?php if (!$config_check['status']): ?>
                    <div class="config-warning">
                        <i class="material-icons left">warning</i>
                        <strong>Configuration Required:</strong> <?php echo $config_check['message']; ?>
                        <br><small>Please update the email settings in email_config.php to enable email functionality.</small>
                    </div>
                <?php endif; ?>

                <?php if ($message): ?>
                    <div class="message <?php echo $messageType; ?>">
                        <i class="material-icons left"><?php echo $messageType === 'success' ? 'check_circle' : 'error'; ?></i>
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <!-- Email Templates -->
                <div class="card">
                    <div class="card-content">
                        <span class="card-title">
                            <i class="material-icons left">description</i>Email Templates
                        </span>
                        <div class="row">
                            <?php foreach ($email_templates as $key => $template): ?>
                                <div class="col s12 m6 l3">
                                    <div class="card template-card z-depth-1" data-template="<?php echo $key; ?>">
                                        <div class="card-content center">
                                            <i class="material-icons large blue-text">
                                                <?php 
                                                $icons = [
                                                    'disaster_alert' => 'warning',
                                                    'evacuation_notice' => 'directions_run',
                                                    'safety_update' => 'info',
                                                    'all_clear' => 'check_circle'
                                                ];
                                                echo $icons[$key] ?? 'email';
                                                ?>
                                            </i>
                                            <p><?php echo ucwords(str_replace('_', ' ', $key)); ?></p>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- Email Form -->
                <div class="card">
                    <div class="card-content">
                        <span class="card-title">
                            <i class="material-icons left">send</i>Compose Email
                        </span>
                        
                        <form method="POST" class="email-form">
                            <input type="hidden" name="template_used" id="template_used" value="">
                            
                            <div class="row">
                                <div class="input-field col s12 m8">
                                    <input type="email" id="to_email" name="to_email" required 
                                           value="<?php echo isset($_POST['to_email']) ? htmlspecialchars($_POST['to_email']) : ''; ?>">
                                    <label for="to_email">To Email Address (Gmail) *</label>
                                </div>
                                <div class="input-field col s12 m4">
                                    <input type="text" id="from_name" name="from_name" 
                                           value="<?php echo isset($_POST['from_name']) ? htmlspecialchars($_POST['from_name']) : ''; ?>">
                                    <label for="from_name">Your Name</label>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="input-field col s12">
                                    <input type="text" id="subject" name="subject" required 
                                           value="<?php echo isset($_POST['subject']) ? htmlspecialchars($_POST['subject']) : ''; ?>">
                                    <label for="subject">Subject *</label>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="input-field col s12">
                                    <textarea id="email_body" name="email_body" class="materialize-textarea" required 
                                              rows="12"><?php echo isset($_POST['email_body']) ? htmlspecialchars($_POST['email_body']) : ''; ?></textarea>
                                    <label for="email_body">Message *</label>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col s12">
                                    <button type="submit" name="send_email" class="btn blue darken-2 waves-effect waves-light" 
                                            <?php echo !$config_check['status'] ? 'disabled' : ''; ?>>
                                        <i class="material-icons left">send</i>Send Email
                                    </button>
                                    <button type="reset" class="btn grey waves-effect waves-light">
                                        <i class="material-icons left">clear</i>Clear Form
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Recent Email Logs -->
                <?php if (!empty($email_logs)): ?>
                <div class="card">
                    <div class="card-content">
                        <span class="card-title">
                            <i class="material-icons left">history</i>Recent Emails (<?php echo count($email_logs); ?>)
                        </span>
                        <?php foreach ($email_logs as $log): ?>
                            <div class="log-item <?php echo $log['status']; ?>">
                                <div class="row valign-wrapper" style="margin-bottom: 0;">
                                    <div class="col s12 m8">
                                        <strong><?php echo htmlspecialchars($log['subject']); ?></strong><br>
                                        <small>To: <?php echo htmlspecialchars($log['to_email']); ?></small>
                                        <?php if (!empty($log['template_used'])): ?>
                                            <br><small>Template: <?php echo ucwords(str_replace('_', ' ', $log['template_used'])); ?></small>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col s12 m4 right-align">
                                        <span class="chip <?php echo $log['status'] === 'sent' ? 'green white-text' : 'red white-text'; ?>">
                                            <?php echo ucfirst($log['status']); ?>
                                        </span><br>
                                        <small><?php echo date('M j, Y g:i A', strtotime($log['sent_at'])); ?></small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            M.AutoInit();
            
            // Template card click handlers
            document.querySelectorAll('.template-card').forEach(function(card) {
                card.addEventListener('click', function() {
                    const templateName = this.dataset.template;
                    loadTemplate(templateName);
                });
            });
            
            function loadTemplate(templateName) {
                const templates = <?php echo json_encode($email_templates); ?>;
                if (templates[templateName]) {
                    document.getElementById('subject').value = templates[templateName].subject;
                    document.getElementById('email_body').value = templates[templateName].body;
                    document.getElementById('template_used').value = templateName;
                    
                    // Update Materialize labels
                    M.updateTextFields();
                    M.textareaAutoResize(document.getElementById('email_body'));
                    
                    // Show success message
                    M.toast({html: 'Template loaded: ' + templateName.replace('_', ' '), classes: 'green'});
                }
            }
        });
    </script>
</body>
</html>
