<?php
require 'db_connect.php';
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

$result = $conn->query("SELECT r.*, u.username FROM reports r JOIN users u ON r.user_id=u.id ORDER BY r.time DESC");
$barangay = $_SESSION['barangay'];
?>
<?php
// header.php
 
 
// Check login status
$isLoggedIn = isset($_SESSION['user_id']);
$isAdmin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
?>

<!DOCTYPE html>
<html>
<head>
  <title>Disaster Watch</title>
  <!-- Materialize CSS and Icons -->
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet">
<style>
  * {
    font-family: 'Source Sans Pro', 'Inter', sans-serif !important;
    font-weight: 400 !important;
  }
   .card-panel {
      height: 120px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: white;
      font-weight: 500;
    }
    .title {
      font-size: 18px;
      margin-bottom: 5px;
    }
    .count {
      font-size: 32px;
      font-weight: bold;
    }
    .note {
      font-size: 12px;
    }
    .updated {
      margin-top: 20px;
      font-size: 12px;
      text-align: right;
      color: #bbb;
    }
    .logo {
      width: 190px;
      position: absolute;
      right: 20px;
      bottom: 20px;
    }
</style>
</head>
<body class="blue-grey lighten-5 ">
    <?php include 'header.php'; ?>
    <h5 style="padding-left:20px;">Summary of Disaster Watch Reporting System</h5>
    <div class="white" style="margin:20px;padding-top:5px;padding-bottom:5px;">

      <p style="padding-left:20px;"><?php echo $_SESSION['barangay']; ?></p>
      <div class=" ">
    
    <div class="row">
      <div class="col s12 m3">
        <div class="card-panel blue">
          <div class="title">Affected Areas</div>
          <div class="count">0</div>
          <div class="note">Based on 0 patients</div>
        </div>
      </div>
      <div class="col s12 m3">
        <div class="card-panel teal darken-3">
          <div class="title">No. of Individuals Affected</div>
          <div class="count">0</div>
          <div class="note">Based on 0 patients</div>
        </div>
      </div>
      <div class="col s12 m3">
        <div class="card-panel orange">
          <div class="title">No. of Families Affected</div>
          <div class="count">0</div>
          <div class="note">No. of Families Affected</div>
        </div>
      </div>
      <div class="col s12 m3">
        <div class="card-panel deep-orange darken-2">
          <div class="title">Site of Evacuation Center</div>
          <div class="count">0</div>
          <div class="note">Based on 0 patients</div>
        </div>
      </div>

      <div class="col s12 m3">
        <div class="card-panel red darken-2">
          <div class="title">No. of Individuals</div>
          <div class="count">0</div>
          <div class="note">Based on 0 patients</div>
        </div>
      </div>
      <div class="col s12 m3">
        <div class="card-panel green darken-2">
          <div class="title">No. of Families</div>
          <div class="count">0</div>
          <div class="note">Based on 0 patients</div>
        </div>
      </div>
      <div class="col s12 m3">
        <div class="card-panel grey darken-2">
          <div class="title">No. of Male</div>
          <div class="count">0</div>
          <div class="note">Based on 0 patients</div>
        </div>
      </div>
      <div class="col s12 m3">
        <div class="card-panel amber darken-3">
          <div class="title">No. of Female</div>
          <div class="count">0</div>
          <div class="note">Based on 0 patients</div>
        </div>
      </div>
      
      <div class="col s12 m3">
        <div class="card-panel light-blue darken-3">
          <div class="title">No. of Pregnant</div>
          <div class="count">0</div>
          <div class="note">Based on 0 patients</div>
        </div>
      </div>
      
      <div class="col s12 m3">
        <div class="card-panel cyan darken-2">
          <div class="title">No. of PWD</div>
          <div class="count">0</div>
          <div class="note">Based on 0 patients</div>
        </div>
      </div>
      
      <div class="col s12 m3">
        <div class="card-panel blue-grey darken-3">

          <div class="title">No. of < 5yo</div>
          <div class="count">0</div>
          <div class="note">Based on 0 patients</div>
        </div>
      </div>
      
      <div class="col s12 m3">
        <div class="card-panel deep-purple darken-3">
          <div class="title">No. of 5-17yo</div>

          <div class="count">0</div>
          <div class="note">Based on 0 patients</div>
        </div>
      </div>
      
      <div class="col s12 m3">
        <div class="card-panel pink darken-3">
          <div class="title">No. of 18-59yo</div>
          <div class="count">0</div>

          <div class="note">Based on 0 patients</div>
        </div>
      </div>
      
      <div class="col s12 m3">
        <div class="card-panel purple darken-3">
          <div class="title">No. of ≥60yo</div>
          <div class="count">0</div>
          <div class="note">Based on 0 patients</div>
        </div>
      </div>
    </div>
    
    <img src="alaga.png" style="bottom:0; position:fixed;" class="logo" alt="BIDA Logo">
  
  </div>
    </div>
</body>

