-- Create email_logs table for tracking sent emails
CREATE TABLE IF NOT EXISTS email_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    to_email VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    body TEXT NOT NULL,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('sent', 'failed') DEFAULT 'sent',
    template_used VARCHAR(100) DEFAULT NULL,
    error_message TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_sent_at (sent_at),
    INDEX idx_status (status),
    INDEX idx_template_used (template_used)
);

-- Add foreign key constraint if users table exists
-- <PERSON><PERSON><PERSON> TAB<PERSON> email_logs ADD CONSTRAINT fk_email_logs_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
