<?php
require '../db_connect.php';
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../login.php");
    exit;
}
$result = $conn->query("SELECT  * FROM users ");
?>
<?php include '../header.php'; ?><br>
<style>
 
  th {
    padding-top:16px !important;
    padding-bottom:16px !important;
  }
  th ,td{
    padding: 10px;
  }
  th,td {
    text-align: center !important;
    font-size:12.6px;
  }
</style>
<div class="container white"style="padding:12px;"> 
<h5>Admin Dashboard - Manage Users</h5>
<table class=" z-depth-2  "  >
  <thead>
    <tr>
      <th></th>
      <th>Id</th><th>Full Name</th><th>User</th><th>Role</th><th>Barangay</th><th>Mobile Number</th><th>Designation</th><th>Status</th> <th>Actions</th>
    </tr>
  </thead>
  <tbody>
  <?php while ($row = $result->fetch_assoc()): ?>
    
    <tr>
     <td>
  <label>
    <input type="checkbox"
           id="approveCheck<?php echo $row['id']; ?>"
           class="filled-in"
           onclick="myFunction(<?php echo $row['id']; ?>)"
           <?php echo ($row['status'] == 'Approved') ? 'checked disabled' : ''; ?> >
    <span></span>
  </label>
</td>

      <td><?php echo $row['id']; ?></td>
      <td><?php echo htmlspecialchars($row['fullname']); ?></td>
      <td><?php echo htmlspecialchars($row['username']); ?></td>
      <td><?php echo htmlspecialchars($row['role']); ?></td>
      <td><?php echo htmlspecialchars($row['barangay']); ?></td>
      <td><?php echo htmlspecialchars($row['mobile_number']); ?></td>
      <td><?php echo htmlspecialchars($row['designation_office']); ?></td>
  <?php if($row['status'] == 'approved') { ?>
        <td><span class="new badge green" data-badge-caption="">Approved</span></td>
      <?php }
        if($row['status'] == 'pending') { ?>
        <td><span class="new badge orange lighten-1" data-badge-caption="">Pending</span></td>
      <?php }
      ?>
      
      <td>
        <?php if ($row['role'] == 'admin'): ?>
         
          
        <?php endif; ?>
        <?php if ($row['status'] == 'pending'): ?>
        <a id="approveBtn<?php echo $row['id']; ?>" disabled href="approve.php?id=<?php echo $row['id']; ?>" class="btn-small btn teal darken-2">Approve</a>
          
         
        <?php endif; ?>
 
      </td>
    </tr>

              


<script>
function myFunction(id) {
  var approveBtn = document.getElementById("approveBtn" + id);
  var checkBox = document.getElementById("approveCheck" + id);

  if (checkBox.checked) {
    approveBtn.removeAttribute("disabled");
  } else {
    approveBtn.setAttribute("disabled", "disabled");
  }
}
</script>

  <?php endwhile; ?>
  </tbody>
</table>






        </div>
<?php include '../footer.php'; ?>
