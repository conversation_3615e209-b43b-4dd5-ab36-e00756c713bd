<?php
session_start();
require 'db_connect.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

$user_id = $_SESSION['user_id'];

// Fetch user data
$stmt = $conn->prepare("SELECT fullname, username, email, barangay, mobile_number, designation_office FROM users WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$user = $result->fetch_assoc();
$stmt->close();

include 'header.php';
?>

<div class="container">
    <div class="row">
        <div class="col s12 m11 offset-m0">
            <div class="card">
                <span class="card-title white-text text-darken-4 black  col m12 s12" style="padding-left:20px;font-size:1.1em; padding-top:10px;padding-bottom:10px;">Profile</span>
                <div class="card-content">
                    <!-- Profile Information -->
                    <div class="row">
                        <i class="material-icons  left   indigo-text text-darken-3 white" style="font-size:15em; border-radius:10em;margin-top:10px;">account_circle</i>
                       <br><br>
                        <div class="input-field col s12 m5">
                            <input id="fullname" type="text" value="<?php echo htmlspecialchars($user['fullname']); ?>" readonly>
                            <label for="fullname" class="active">Full Name</label>
                        </div>
                        <div class="input-field col s12 m3">
                            <input id="username" type="text" value="<?php echo htmlspecialchars($user['username']); ?>" readonly>
                            <label for="username" class="active">Username</label>
                        </div>
                        <div class="input-field col s12 m3">
                            <input id="email" type="email" value="<?php echo htmlspecialchars($user['email']); ?>" readonly>
                            <label for="email" class="active">Email</label>
                        </div>
                        <div class="input-field col s12 m4">
                            <input id="barangay" type="text" value="<?php echo htmlspecialchars($user['barangay']); ?>" readonly>
                            <label for="barangay" class="active">Barangay</label>
                        </div>
                        <div class="input-field col s12 m4">
                            <input id="mobile" type="text" value="<?php echo htmlspecialchars($user['mobile_number']); ?>" readonly>
                            <label for="mobile" class="active">Mobile Number</label>
                        </div>
                        <div class="input-field col s12 m3">
                            <input id="designation" type="text" value="<?php echo htmlspecialchars($user['designation_office']); ?>" readonly>
                            <label for="designation" class="active">Designation/Office</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Change Password Section -->
            <div class="card">
                <span class="card-title white-text text-darken-4 black  col m12 s12" style="padding-left:20px;font-size:1.1em; padding-top:10px;padding-bottom:10px;">Change Password</span>
                <div class="card-content">

                    <form id="changePasswordForm">
                        <div class="row">
                            <div class="input-field col s12">
                                <input id="current_password" name="current_password" type="password" required>
                                <label for="current_password">Current Password</label>
                            </div>
                            <div class="input-field col s12">
                                <input id="new_password" name="new_password" type="password" required>
                                <label for="new_password">New Password</label>
                            </div>
                            <div class="input-field col s12">
                                <input id="confirm_password" name="confirm_password" type="password" required>
                                <label for="confirm_password">Confirm New Password</label>
                            </div>
                            <div class="col s12">
                                <button class="btn blue waves-effect" type="submit">Change Password</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const currentPassword = document.getElementById('current_password').value;
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    // Validate passwords match
    if (newPassword !== confirmPassword) {
        M.toast({html: 'New passwords do not match!', classes: 'red'});
        return;
    }
    
    // Validate password length
    if (newPassword.length < 6) {
        M.toast({html: 'Password must be at least 6 characters long!', classes: 'red'});
        return;
    }
    
    const formData = new FormData();
    formData.append('current_password', currentPassword);
    formData.append('new_password', newPassword);
    
    fetch('change_password.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            M.toast({html: data.message, classes: 'green'});
            document.getElementById('changePasswordForm').reset();
        } else {
            M.toast({html: data.message, classes: 'red'});
        }
    })
    .catch(error => {
        console.error('Error:', error);
        M.toast({html: 'An error occurred. Please try again.', classes: 'red'});
    });
});
</script>

<?php include 'footer.php'; ?>