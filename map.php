 
<?php
require 'db_connect.php';
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

$result = $conn->query("SELECT r.*, u.username FROM reports r JOIN users u ON r.user_id=u.id ORDER BY r.time DESC");
$barangay = $_SESSION['barangay'];
?>
<?php
// header.php
 
 
// Check login status
$isLoggedIn = isset($_SESSION['user_id']);
$isAdmin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
      .info { 
        padding: 10px 20px;
        font:14px/15px Arial, Helvetica, sans-serif;
        background: white;
        
        border:1px solid #90a4ae; border-radius: 2px; 
    } 
      .info h5{ font-weight: 500; color:black; }
		
    </style>
</head>
<body class="blue-grey lighten-5">
  
 
<?php include 'header.php'; ?>

 <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

   <div class=" " style="  ">
<h4 style="padding-left:10px; font-size:15.1px;">All Disaster Reports Map (Parañaque City)</h4>
 
    <div class="  white   left  ">
<div id="map" class="col  s12" style="width: 1520px; height: 550px; min-width: 10px !important;"></div>
</div>

 
 
 <script>
        // Initialize the map centered at Parañaque City
        var map = L.map('map').setView([14.4850184,121.0673404], 12 ); // Sample coords


        // Add OpenStreetMap tiles
        L.tileLayer('http://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}.png', {
           
        }).addTo(map);

        // Health Centers with Coordinates
        var healthCenters = [
            { name: "VITALEZ HEALTH CENTER", coords: [14.508007,121.0060204] },
            { name: "SAN ISIDRO ELEMENTARY SCHOOL", coords: [14.4703097,121.0057859] },
            { name: "SAN ANTONIO HEALTH CENTER", coords: [14.4710524,121.0273762] },
          
            { name: "BACLARAN HEALTH CENTER", coords: [14.5293829,120.9958199] },
            { name: "SUN VALLEY HEALTH CENTER", coords: [14.4899553,121.029691] },
            { name: "SAN MARTIN DE PORRES HEALTH CENTER", coords: [14.4944448,121.042808] },
            { name: "LA HUERTA ELEMENTARY SCHOOL", coords: [14.498697403296449, 120.9932453429844] },
            { name: "STO.NIÑO HEALTH CENTER", coords: [14.501,120.9953121] },
            { name: "MARCELO GREEN HEALTH CENTER", coords: [14.4763622,121.0401454] },
            { name: "MERVILLE HEALTH CENTER", coords: [14.5001135,121.0262862] },
            { name: "DON BOSCO NATIONAL HIGHSCHOOL", coords: [14.4816647,121.01544] },
            { name: "DON GALO SPORTS COMPLEX", coords: [14.502948,120.9882588] },
            { name: "TAMBO ELEMENTARY SCHOOL", coords: [14.515809,120.9930932] },
            { name: "BF ELEMENTARY SCHOOL", coords: [14.4575733,121.0266431] },
            { name: "MOONWALK NATIONAL HIGHSCHOOL", coords: [14.4923219,121.0037391] },
            { name: "SAN DIONISIO HEALTH CENTER", coords: [14.4945641,120.9895385] },
            { name: "Parañaque National High School Main", coords: [14.4794342,120.9961137] },
            { name: "Don Bosco Gym", coords: [14.4818301,121.0200573] },
            { name: "SAN ANTONIO BARANGAY HALL", coords: [14.4648932,120.9965805] },
            { name: "Area 1 Disaster Building, Sitio Libjo", coords: [14.5017372,121.0063476] },
            { name: "Beldevere Covered Court", coords: [14.5024162,121.0240401] },
            { name: "Don Galo Gym", coords: [14.502948,120.9882588] },
            { name: "San Antonio Elementary School-Silverio Annex", coords: [14.4714932,121.0096087] },
            { name: "Dr. FC Santos Compound", coords: [14.49891,121.0042511] },
            { name: "Palanyag Gymnasium", coords: [14.487452,120.986588] },
            { name: "Belvedere Covered Court", coords: [14.5027563,121.0246038] }
        ];
       
     
         var pqueBoundary = [
[14.529579, 120.979136],
[14.531273, 120.992836],
[14.532973, 120.992691],
[14.533463, 120.996525],
[14.533078, 120.998139],
[14.532659, 120.998824],
[14.531898, 120.999142],
[14.531695, 120.999410],
[14.530841, 120.999301],
[14.529572, 120.999811],
[14.528936, 120.999715],
[14.527969, 120.999270],
[14.527496, 120.998357],
[14.527267, 120.998323],
[14.523316, 120.998938],
[14.521861, 120.998911],
[14.520720, 120.999073],
[14.519684, 120.999456],
[14.516958, 120.998031],
[14.516852, 121.000507],
[14.514175, 121.001035],
[14.512926, 121.000964],
[14.511998, 121.001123],
[14.511832, 121.001096],
[14.511832, 121.000870],
[14.511455, 121.000747],
[14.511492, 121.000321],
[14.510237, 121.000222],
[14.507662, 120.999495],
[14.506802, 121.001214],
[14.509445, 121.002160],
[14.509475, 121.002714],
[14.509805, 121.003419],
[14.509727, 121.003844],
[14.510542, 121.004218],
[14.511113, 121.004716],
[14.510697, 121.005362],
[14.510810, 121.007280],
[14.510583, 121.007698],
[14.509616, 121.007900],
[14.509387, 121.008686],
[14.508553, 121.009096],
[14.508428, 121.009760],
[14.508162, 121.009860],
[14.504173, 121.014422],
[14.502621, 121.017066],
[14.502661, 121.017778],
[14.502098, 121.019378],
[14.502361, 121.020159],
[14.502685, 121.020280],
[14.504382, 121.021911],
[14.505247, 121.023440],
[14.504799, 121.023909],
[14.504748, 121.024457],
[14.505119, 121.025249],
[14.505547, 121.027830],
[14.504991, 121.029039],
[14.506582, 121.031840],
[14.506847, 121.033999],
[14.506242, 121.034598],
[14.506374, 121.035273],
[14.503921, 121.036551],
[14.504670, 121.038810],
[14.503310, 121.041956],
[14.501630, 121.043887],
[14.500129, 121.046878],
[14.498698, 121.047168],
[14.497591, 121.047263],
[14.497570, 121.047062],
[14.496460, 121.047171],
[14.496486, 121.047378],
[14.495601, 121.047469],
[14.495548, 121.047078],
[14.494458, 121.047167],
[14.494518, 121.047538],
[14.494139, 121.047599],
[14.494139, 121.047599],
[14.492930, 121.047341],
[14.492747, 121.047707],
[14.491728, 121.047521],
[14.491419, 121.047320],
[14.486520, 121.046478],
[14.486139, 121.046151],
[14.484450, 121.046228],
[14.481837, 121.045909],
[14.480888, 121.046180],
[14.480140, 121.046617],
[14.480055, 121.046159],
[14.480178, 121.046158],
[14.480170, 121.045799],
[14.480678, 121.045221],
[14.445789, 121.045332],
[14.445331, 121.044568],
[14.444626, 121.043978],
[14.443587, 121.043675],
[14.443762, 121.042416],
[14.443534, 121.041611],
[14.443897, 121.041391],
[14.443836, 121.041068],
[14.442992, 121.040504],
[14.443008, 121.040171],
[14.442521, 121.040139],
[14.442177, 121.039880],
[14.442081, 121.039607],
[14.442308, 121.039270],
[14.442158, 121.038849],
[14.441686, 121.038673],
[14.441202, 121.037791],
[14.440880, 121.037800],
[14.440131, 121.036429],
[14.441300, 121.034958],
[14.441431, 121.034500],
[14.441898, 121.034225],
[14.442460, 121.033151],
[14.440680, 121.028898],
[14.439400, 121.026852],
[14.439090, 121.026862],
[14.437730, 121.025460],
[14.437211, 121.024304],
[14.436172, 121.024318],
[14.436069, 121.022328],
[14.435280, 121.020760],
[14.436528, 121.019962],
[14.436659, 121.019016],
[14.437478, 121.017231],
[14.437478, 121.016381],
[14.437649, 121.016032],
[14.437901, 121.015958],
[14.438138, 121.016108],
[14.438698, 121.015529],
[14.439078, 121.015390],
[14.440728, 121.015890],
[14.441658, 121.016536],
[14.443995, 121.015931],
[14.444832, 121.016229],
[14.445837, 121.016831],
[14.447067, 121.015982],
[14.447360, 121.014729],
[14.447813, 121.014338],
[14.448560, 121.013079],
[14.449593, 121.012960],
[14.450281, 121.012476],
[14.450958, 121.012386],
[14.451618, 121.011621],
[14.452585, 121.011212],
[14.453969, 121.006538],
[14.455019, 121.004118],
[14.454003, 121.002821],
[14.453732, 121.001750],
[14.454080, 121.001573],
[14.454350, 121.001769],
[14.454889, 121.001838],
[14.455827, 121.002589],
[14.457232, 121.001720],
[14.457800, 121.002088],
[14.457830, 121.002567],
[14.457939, 121.002592],
[14.460440, 121.002134],
[14.460769, 121.002352],
[14.461657, 121.002083],
[14.466082, 121.001934],
[14.467255, 121.002213],
[14.467818, 121.001392],
[14.469150, 121.000651],
[14.471218, 120.998849],
[14.471613, 120.997989],
[14.471909, 120.998025],
[14.472648, 120.997385],
[14.474253, 120.996720],
[14.474465, 120.996321],
[14.474379, 120.995741],
[14.474977, 120.995439],
[14.474990, 120.994733],
[14.475308, 120.994383],
[14.475489, 120.993567],
[14.475747, 120.993160],
[14.476044, 120.993258],
[14.476221, 120.993139],
[14.476027, 120.992026],
[14.475692, 120.991809],
[14.476212, 120.991120],
[14.476456, 120.991074],
[14.476998, 120.991568],
[14.476912, 120.991671],
[14.477119, 120.991743],
[14.477503, 120.991225],
[14.477300, 120.990949],
[14.478326, 120.990350],
[14.479539, 120.991489],
[14.480069, 120.991560],
[14.480252, 120.992213],
[14.481257, 120.992539],
[14.482947, 120.992790],
[14.483179, 120.992262],
[14.482923, 120.991919],
[14.482979, 120.991248],
[14.482821, 120.990730],
[14.483259, 120.990112],
[14.483020, 120.989723],
[14.482294, 120.989491],
[14.483935, 120.988271],
[14.483878, 120.987869],
[14.484169, 120.987209],
[14.484122, 120.986717],
[14.484696, 120.986062],
[14.484443, 120.985810],
[14.484928, 120.984850],
[14.485310, 120.985027],
[14.486379, 120.982541],
[14.487461, 120.980773],
[14.487680, 120.980910],
[14.487701, 120.981190],
[14.487972, 120.981337],
[14.488133, 120.981232],
[14.488269, 120.981458],
[14.488369, 120.981334],
[14.488728, 120.981530],
[14.490014, 120.981551],
[14.490957, 120.982039],
[14.491427, 120.981988],
[14.491657, 120.982217],
[14.491633, 120.982512],
[14.492772, 120.980559],
[14.492371, 120.979429],
[14.492109, 120.979352],
[14.492226, 120.979122],
[14.491891, 120.979088],
[14.491915, 120.978877],
[14.491630, 120.978953],
[14.491393, 120.979278],
[14.491329, 120.979043],
[14.491046, 120.978872],
[14.491029, 120.979185],
[14.490562, 120.979201],
[14.491115, 120.978271],
[14.490954, 120.978110],
[14.490961, 120.978337],
[14.490778, 120.978292],
[14.490284, 120.979067],
[14.490161, 120.978892],
[14.489980, 120.979087],
[14.489470, 120.978947],
[14.489521, 120.978821],
[14.489888, 120.978959],
[14.490290, 120.978669],
[14.490382, 120.978453],
[14.490038, 120.978273],
[14.489518, 120.978241],
[14.489317, 120.978871],
[14.489317, 120.978871],
[14.488881, 120.978603],
[14.489115, 120.978632],
[14.489316, 120.978150],
[14.488701, 120.977890],
[14.488656, 120.977725],
[14.488322, 120.978340],
[14.488731, 120.978600],
[14.488271, 120.978551],
[14.488153, 120.978281],
[14.488306, 120.977991],
[14.487624, 120.977474],
[14.487628, 120.977173],
[14.487352, 120.977057],
[14.487418, 120.976949],
[14.486942, 120.976762],
[14.486689, 120.976131],
[14.486102, 120.975963],
[14.485554, 120.975509],
[14.485478, 120.974850],
[14.484930, 120.974532],
[14.484611, 120.974580],
[14.483576, 120.973546],
[14.482712, 120.973050],
[14.482199, 120.972504],
[14.481680, 120.972498],
[14.481749, 120.972322],
[14.481407, 120.972031],
[14.483371, 120.972537],
[14.484666, 120.973223],
[14.486521, 120.973663],
[14.490162, 120.976300],
[14.490967, 120.976722],
[14.492328, 120.976761],
[14.492442, 120.976381],
[14.492322, 120.975682],
[14.492688, 120.975622],
[14.492677, 120.978260],
[14.492811, 120.979210],
[14.492971, 120.979443],
[14.492888, 120.979906],
[14.493181, 120.980946],
[14.493451, 120.981188],
[14.494587, 120.981494],
[14.497506, 120.981733],
[14.499759, 120.982181],
[14.501981, 120.981750],
[14.502039, 120.982797],
[14.503321, 120.984842],
[14.502799, 120.985328],
[14.502439, 120.986587],
[14.502370, 120.987470],
[14.501954, 120.988057],
[14.501581, 120.987728],
[14.501568, 120.987393],
[14.501169, 120.987575],
[14.500648, 120.987443],
[14.500527, 120.986916],
[14.499959, 120.986518],
[14.500224, 120.986076],
[14.500043, 120.986135],
[14.499893, 120.985740],
[14.499644, 120.985773],
[14.499762, 120.985658],
[14.499682, 120.985567],
[14.499477, 120.985635],
[14.498930, 120.985431],
[14.498714, 120.985197],
[14.498192, 120.984765],
[14.497563, 120.984502],
[14.496531, 120.983511],
[14.492952, 120.981206],
[14.492065, 120.982310],
[14.491804, 120.982895],
[14.491836, 120.984526],
[14.492007, 120.984772],
[14.492024, 120.985325],
[14.495243, 120.987443],
[14.496339, 120.987752],
[14.497007, 120.988129],
[14.499641, 120.988849],
[14.500376, 120.988749],
[14.502032, 120.989160],
[14.502083, 120.989593],
[14.502597, 120.989729],
[14.508107, 120.974224],
[14.514385, 120.977896],
[14.518807, 120.979330],
[14.522535, 120.979448],
[14.529568, 120.979140],
 
        ];
        
        // PHP Data for Barangay counts
        var barangayData = 7;

        // Function to generate popup content dynamically
        function generatePopup(name) {
            let total = barangayData[name] || 1;
            let male = Math.floor(total * 0.52); // Assuming ~52% male
            let female = total - male;
            return `<b>${name}</b><br>MALE: ${male}<br>FEMALE: ${female}<br>TOTAL: ${total}`;
        }

       
    const pquePolygon = L.polygon(pqueBoundary, {
        color: '#455a64  ',
        fillColor: '#455a64     ',
        weight: 0,
        opacity: 0.1,
        fillOpacity: 0.5
}).addTo(map);


        // Add circles for each health center
    healthCenters.forEach(function(center) {
    const circle = L.circle(center.coords, {
        color: 'black ',
        fillColor: '#00c853  ',
        fillOpacity: 1,
        opacity: 0.9,
        radius:  160
    }).addTo(map)
      .bindPopup(generatePopup(center.name))
      .on('mouseover', function () {
    this.openPopup();
});

});
   
L.polyline([pqueBoundary], {
      dashArray: "7 7",
      color: "#455a64 ",
      weight: 1,

    }).addTo(map);

     
const info = L.control();

	info.onAdd = function (map) {
		this._div = L.DomUtil.create('div', 'info');
		this.update();
		return this._div;
	};

	info.update = function (props) {
		const contents = props ? `<b>${props.name}</b><br />${props.density} people / mi<sup>2</sup>` : '<h5>Legends</h5><span class="green accent-4" style="padding:5px 13px;border-radius:3px;"></span>&nbspModerate<br><br><span class="orange" style="padding:5px 13px;border-radius:3px;"></span>&nbspMedium<br><br><span class="red" style="padding:5px 13px;border-radius:3px;"></span>&nbspBe Prepared';
		this._div.innerHTML = `${contents}`;
	};

	info.addTo(map);





    
    </script>
<?php include 'footer.php'; ?>
  
</body>
</html>