<?php
require 'db_connect.php';
session_start();

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

try {
    $sql = "SELECT * FROM evacuation_reports WHERE 1=1";
    $params = [];
    $types = "";
    
    // Apply event type filter
    if (!empty($_POST['event_type'])) {
        $eventType = $_POST['event_type'];
        $sql .= " AND (`$eventType` IS NOT NULL AND `$eventType` != '' AND `$eventType` != '0')";
    }
    
    // Apply date from filter
    if (!empty($_POST['date_from'])) {
        $sql .= " AND activity_date >= ?";
        $params[] = $_POST['date_from'];
        $types .= "s";
    }
    
    // Apply date to filter
    if (!empty($_POST['date_to'])) {
        $sql .= " AND activity_date <= ?";
        $params[] = $_POST['date_to'];
        $types .= "s";
    }
    
    // Apply barangay filter
    if (!empty($_POST['barangay'])) {
        $sql .= " AND barangay = ?";
        $params[] = $_POST['barangay'];
        $types .= "s";
    }
    
    // Apply user's barangay restriction if not admin
    $userBarangay = $_SESSION['barangay'] ?? '';
    $userRole = $_SESSION['role'] ?? '';
    
    if ($userRole !== 'admin' && !empty($userBarangay)) {
        $sql .= " AND barangay = ?";
        $params[] = $userBarangay;
        $types .= "s";
    }
    
    $sql .= " ORDER BY activity_date DESC, id DESC";
    
    // Execute query
    if (!empty($params)) {
        $stmt = $conn->prepare($sql);
        if (!empty($types)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
    } else {
        $result = $conn->query($sql);
    }
    
    if (!$result) {
        throw new Exception("Query failed: " . $conn->error);
    }
    
    $records = [];
    while ($row = $result->fetch_assoc()) {
        $records[] = $row;
    }
    
    echo json_encode([
        'success' => true,
        'records' => $records,
        'count' => count($records)
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error fetching data: ' . $e->getMessage()
    ]);
}

$conn->close();
?>