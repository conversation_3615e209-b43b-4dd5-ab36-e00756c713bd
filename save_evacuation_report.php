<?php
require 'db_connect.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
  $stmt = $conn->prepare("INSERT INTO evacuation_reports (
    activity_date,GEOLOGIC, WEATHER,MANMADE,activity_time, barangay,additional_info, affected_areas, individuals_affected,
    families_affected, evacuation_site, evac_individuals, evac_families,
    evac_male, evac_female, evac_pregnant, evac_pwd,
    evac_under_5, evac_5_to_17, evac_18_to_59, evac_60_above,no_of_casualties,no_of_treated_on_site,no_of_brought_to_hospital,
    no_of_deaths,no_of_missing,cause_1_d,no_of_zero_to_fifteen_d,no_of_greater_than_fifteen_d,total_cause_1_d,cause_2_d,no_of_zero_to_fifteen_d_c2,
    no_of_greater_than_fifteen_d_c2,total_cause_2_d,cause_3_d,no_of_zero_to_fifteen_d_c3,no_of_greater_than_fifteen_d_c3,total_cause_3_d,cause_4_d,
    no_of_zero_to_fifteen_d_c4,no_of_greater_than_fifteen_d_c4,total_cause_4_d,cause_5_d,no_of_zero_to_fifteen_d_c5,no_of_greater_than_fifteen_d_c5,
    total_cause_5_d,causes_1_top_five,no_of_zero_to_fifteen_d_c1_top_five,no_of_greater_than_fifteen_d_c1_top_five,total_cause_1_top_five_top_five,
    causes_2_top_five,no_of_zero_to_fifteen_d_c2_top_five,no_of_greater_than_fifteen_d_c2_top_five,total_cause_2_top_five,causes_3_top_five,no_of_zero_to_fifteen_d_c3_top_five,
    no_of_greater_than_fifteen_d_c3_top_five,total_cause_3_top_five,causes_4_top_five,no_of_zero_to_fifteen_d_c4_top_five,no_of_greater_than_fifteen_d_c4_top_five,
    total_cause_4_top_five,causes_5_top_five,no_of_zero_to_fifteen_d_c5_top_five,no_of_greater_than_fifteen_d_c5_top_five,total_cause_5_top_five,service_1_services_provided,
    service_2_services_provided,service_3_services_provided,service_4_services_provided,service_5_services_provided,essential_drug_1,stock_quantity_ed1,stock_quantity_remaining_ed1,
    essential_drug_2,quantity_given_ed2,stock_quantity_remaining_ed2,essential_drug_3,quantity_given_ed3,stock_quantity_remaining_ed3,essential_drug_4,
    quantity_given_ed4,stock_quantity_remaining_ed4,essential_drug_5,quantity_given_ed5,stock_quantity_remaining_ed5,communication,remarks_communication,
    electric_power,remarks_electric_power,water,remarks_water,roads_bridges,remarks_road_bridges,Other,remarks_other,problems_encountered,recommendations,
    quantity_given_ed1,stock_quantity_ed2,stock_quantity_ed3,stock_quantity_ed4,stock_quantity_ed5,service_1_no_of_patient,service_2_no_of_patient,
    service_3_no_of_patient,service_4_no_of_patient,service_5_no_of_patient,date_prepared,mobile_no,printed_name,email,designation_office


  ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

  $stmt->bind_param("sssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss",
  $_POST['activity_date'],$_POST['GEOLOGIC'],$_POST['WEATHER'],$_POST['MANMADE'], $_POST['activity_time'], $_POST['barangay'],
  $_POST['additional_info'],
  $_POST['affected_areas'], $_POST['individuals_affected'], $_POST['families_affected'],
  $_POST['evacuation_site'], $_POST['evac_individuals'], $_POST['evac_families'],
  $_POST['evac_male'], $_POST['evac_female'], $_POST['evac_pregnant'],
  $_POST['evac_pwd'], $_POST['evac_under_5'], $_POST['evac_5_to_17'],
  $_POST['evac_18_to_59'], $_POST['evac_60_above'], $_POST['no_of_casualties'], $_POST['no_of_treated_on_site'], $_POST['no_of_brought_to_hospital'],
  $_POST['no_of_deaths'], $_POST['no_of_missing'], $_POST['cause_1_d'], $_POST['no_of_zero_to_fifteen_d'], $_POST['no_of_greater_than_fifteen_d'], 
  $_POST['total_cause_1_d'], $_POST['cause_2_d'], $_POST['no_of_zero_to_fifteen_d_c2'], $_POST['no_of_greater_than_fifteen_d_c2'], $_POST['total_cause_2_d'],
  $_POST['cause_3_d'], $_POST['no_of_zero_to_fifteen_d_c3'], $_POST['no_of_greater_than_fifteen_d_c3'], $_POST['total_cause_3_d'], $_POST['cause_4_d'],
  $_POST['no_of_zero_to_fifteen_d_c4'], $_POST['no_of_greater_than_fifteen_d_c4'], $_POST['total_cause_4_d'], $_POST['cause_5_d'], $_POST['no_of_zero_to_fifteen_d_c5'],
  $_POST['no_of_greater_than_fifteen_d_c5'], $_POST['total_cause_5_d'], $_POST['causes_1_top_five'], $_POST['no_of_zero_to_fifteen_d_c1_top_five'], $_POST['no_of_greater_than_fifteen_d_c1_top_five'],
  $_POST['total_cause_1_top_five_top_five'],$_POST['causes_2_top_five'],$_POST['no_of_zero_to_fifteen_d_c2_top_five'],$_POST['no_of_greater_than_fifteen_d_c2_top_five'],$_POST['total_cause_2_top_five'],
  $_POST['causes_3_top_five'],$_POST['no_of_zero_to_fifteen_d_c3_top_five'],$_POST['no_of_greater_than_fifteen_d_c3_top_five'],$_POST['total_cause_3_top_five'],$_POST['causes_4_top_five'],
  $_POST['no_of_zero_to_fifteen_d_c4_top_five'],$_POST['no_of_greater_than_fifteen_d_c4_top_five'],$_POST['total_cause_4_top_five'],$_POST['causes_5_top_five'],
  $_POST['no_of_zero_to_fifteen_d_c5_top_five'],$_POST['no_of_greater_than_fifteen_d_c5_top_five'], $_POST['total_cause_5_top_five'],$_POST['service_1_services_provided'],$_POST['service_2_services_provided'],
  $_POST['service_3_services_provided'],$_POST['service_4_services_provided'],$_POST['service_5_services_provided'],$_POST['essential_drug_1'],$_POST['stock_quantity_ed1'],
  $_POST['stock_quantity_remaining_ed1'],$_POST['essential_drug_2'],$_POST['quantity_given_ed2'],$_POST['stock_quantity_remaining_ed2'],$_POST['essential_drug_3'],
  $_POST['quantity_given_ed3'],$_POST['stock_quantity_remaining_ed3'],$_POST['essential_drug_4'],$_POST['quantity_given_ed4'],$_POST['stock_quantity_remaining_ed4'],
  $_POST['essential_drug_5'],$_POST['quantity_given_ed5'],$_POST['stock_quantity_remaining_ed5'],$_POST['communication'],$_POST['remarks_communication'],
  $_POST['electric_power'],$_POST['remarks_electric_power'],$_POST['water'],$_POST['remarks_water'],$_POST['roads_bridges'],$_POST['remarks_road_bridges'],
  $_POST['Other'],$_POST['remarks_other'],$_POST['problems_encountered'],$_POST['recommendations'], $_POST['quantity_given_ed1'],$_POST['stock_quantity_ed2'],
  $_POST['stock_quantity_ed3'],$_POST['stock_quantity_ed4'],$_POST['stock_quantity_ed5'],$_POST['service_1_no_of_patient'],$_POST['service_2_no_of_patient'],
  $_POST['service_3_no_of_patient'],$_POST['service_4_no_of_patient'],$_POST['service_5_no_of_patient'],$_POST['date_prepared'],$_POST['mobile_no'],
  $_POST['printed_name'],$_POST['email'],$_POST['designation_office']
  );
$required = [
 
  'activity_date','GEOLOGIC','WEATHER','MANMADE', 'activity_time', 'barangay','additional_info', 'affected_areas',
  'individuals_affected', 'families_affected', 'evacuation_site',
  'evac_individuals', 'evac_families', 'evac_male', 'evac_female',
  'evac_pregnant', 'evac_pwd', 'evac_under_5', 'evac_5_to_17',
  'evac_18_to_59', 'evac_60_above', 'no_of_casualties', 'no_of_treated_on_site', 'no_of_brought_to_hospital',
  'no_of_deaths'
];

foreach ($required as $field) {
  if (empty($_POST[$field])) {
    echo "Missing required field: " . htmlspecialchars($field);
    exit;
  }
}

  if ($stmt->execute()) {
    echo "Evacuation report submitted successfully.";
  } else {
    echo "Failed to save report: " . $stmt->error;
  }

  $stmt->close();
  $conn->close();
}
?>
