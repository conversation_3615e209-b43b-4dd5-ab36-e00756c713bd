<?php
require 'db_connect.php';
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}
$message = "";
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $type = $conn->real_escape_string($_POST['type']);
    $location = $conn->real_escape_string($_POST['location']);
    $time = $conn->real_escape_string($_POST['time']);
    $description = $conn->real_escape_string($_POST['description']);
    $user_id = $_SESSION['user_id'];
    // Handle file upload
    $imagePath = NULL;
    if (!empty($_FILES["image"]["name"])) {
        $targetDir = "uploads/";
        $fileName = basename($_FILES["image"]["name"]);
        $targetFile = $targetDir . $fileName;
        $fileType = strtolower(pathinfo($targetFile, PATHINFO_EXTENSION));
        // Simple check for allowed image types
        if (in_array($fileType, ['jpg','jpeg','png','gif'])) {
            if (move_uploaded_file($_FILES["image"]["tmp_name"], $targetFile)) {  //:contentReference[oaicite:11]{index=11}
                $imagePath = $targetFile;
            }
        }
    }
    // Insert report
    $stmt = $conn->prepare("INSERT INTO reports (user_id,type,location,time,description,image) VALUES (?,?,?,?,?,?)");
    $stmt->bind_param("isssss", $user_id, $type, $location, $time, $description, $imagePath);
    if ($stmt->execute()) {
        $message = "Report submitted and pending approval.";
    } else {
        $message = "Error: " . $stmt->error;
    }
    $stmt->close();
}
?>
<?php include 'header.php'; ?>
<form action="submit_report.php" class="white" method="post" enctype="multipart/form-data" style="padding:10px;">
  <h4>Submit Disaster Report</h4>
  <div class="input-field">
    <select name="type" required>
      <option value="" disabled selected>Choose type</option>
      <option>Earthquake</option>
      <option>Flood</option>
      <option>Fire</option>
      <option>Storm</option>
    </select>
    <label>Disaster Type</label>
  </div>
  <div class="input-field">
    <input id="location" name="location" type="text" required>
    <label for="location">Location</label>
  </div>
  <div class="input-field">
    <input id="time" name="time" type="datetime-local" required>
    <label for="time" class="active">Date and Time</label>
  </div>
  <div class="input-field">
    <textarea id="description" name="description" class="materialize-textarea" required></textarea>
    <label for="description">Description</label>
  </div>
  <div class="file-field input-field">
    <div class="btn">
      <span>Image</span>
      <input type="file" name="image" accept=".jpg,.jpeg,.png,.gif">
    </div>
    <div class="file-path-wrapper">
      <input class="file-path validate" type="text">
    </div>
  </div>
  <button class="btn blue waves-effect" type="submit">Submit Report</button>
</form>
<p><?php echo $message; ?></p>
<?php include 'footer.php'; ?>
