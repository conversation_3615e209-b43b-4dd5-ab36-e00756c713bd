<?php
// header.php
 error_reporting(0);
 
// Check login status
$isLoggedIn = isset($_SESSION['user_id']);
$isAdmin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
?>

<!DOCTYPE html>
<html>
<head>
  <title>Disaster Watch Reporting System</title>
 <link rel="icon" href="favicon.ico" type="image/x-icon" />
      <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
     
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Compiled and minified CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">

    <!-- Compiled and minified JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
  <!-- Materialize CSS and Icons -->
  
  <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet">

<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  
<style>
  * {
    font-family: 'Source Sans Pro', 'Inter', sans-serif  ;
    
  }
</style>
</head>
<body class="grey lighten-4">
    
  
 
 
        
<nav class="nav-wrapper indigo darken-4" >
   <ul id="slide-out" class="sidenav">
   <?php if ($isLoggedIn): ?>
  <?php if ($isAdmin): ?>
 
      <li class=""><p class="small-text black-text" style="margin-left:28px;"> Disaster Watch Reporting System </p></li>
   <li><a href="#"><span class="blue-text text-darken-3" style="font-size:13.6px;">Parañaque City Health Office</span></a></li>
    
   <li  ><a href="/disaster">  Dashboard</a></li>
   <li  ><a style="font-weight:300;" href="/disaster/add_health_center_report.php">Data Entry</a></li>
    <li ><a style="font-weight:300;" href="/disaster/profile.php">Profile</a></li>
   <li  ><a style="font-weight:300;" href="/disaster/records.php">Records</a></li>
   <li  ><a style="font-weight:300;" href="/disaster/view_reports.php">Data Analysis</a></li>
   <li  ><a style="font-weight:300;" href="/disaster/map.php">Map</a></li>
   <li  ><a style="font-weight:300;" href="admin/approve.php">Approve</a></li>
    <li  ><a style="font-weight:300;" href="logout.php">Logout</a></li>
      
       
       <?php else: ?>
          
  <li><a href="#"><span class="blue-text text-darken-3">Disaster Watch Reporting System</span></a></li>
  <li><a href="#"><span class="blue-text text-darken-3">Parañaque City Health Office</span></a></li>
        <li ><a style="font-weight:300;" href="index.php">Dashboard</a></li>
        <li  ><a style="font-weight:300;" href="add_health_center_report.php">Data Entry</a></li>
         <li ><a style="font-weight:300;" href="profile.php">Profile</a></li>
        <li  ><a style="font-weight:300;" href="records.php">Records</a></li>
        <li  ><a style="font-weight:300;" href="view_reports.php">Data Analysis</a></li>
        <li  ><a style="font-weight:300;" href="map.php">Map</a></li>
        <li  ><a href="logout.php">Logout</a></li>
          <?php endif; ?>
        <?php else: ?>
            <li><img src="" alt=""></li>
          <li  ><a href="register.php">Register</a></li>
          <li  ><a href="login.php">Login</a></li>
        
        <?php endif; ?>
  </ul>
  <a href="#" data-target="slide-out" class="sidenav-trigger" style="display:block;"><i class="material-icons">menu</i></a>
    <div class="nav-wrapper ">
      
      <ul id="nav-mobile " class="  "  >
        
          <?php if ($isLoggedIn): ?>
  <?php if ($isAdmin): ?>
    
    <li class="right indigo darken-3" style="padding-left:6px; padding-right:6px; "><?php echo $_SESSION['barangay']; ?></li>
     
    
     
      
       
       <?php else: ?>
    <li class="right indigo darken-3" style="padding-left:6px; padding-right:6px; "><?php echo $_SESSION['barangay']; ?></li>

        <li class="right"><a href="logout.php">Logout</a></li>
        <li class="right"><a style="font-weight:300;" href="map.php">Map</a></li>
        <li class="right"><a style="font-weight:300;" href="view_reports.php">Data Analysis</a></li>
        <li class="right"><a style="font-weight:300;" href="records.php">Records</a></li>
        <li class="right"><a style="font-weight:300;" href="add_health_center_report.php">Data Entry</a></li>
        <li class="right"><a style="font-weight:300;" href="profile.php">Profile</a></li>
          <li class="right"><a style="font-weight:300;" href="index.php">Dashboard</a></li>
          <?php endif; ?>
        <?php else: ?>
          <li class="right"><a href="register.php">Register</a></li>
          <li class="right"><a href="login.php">Login</a></li>
        
        <?php endif; ?>
        
          <li><a class="brand-logo  left " style="padding-left:0px; font-size:17px;font-weight:500;" href="#" >Disaster Watch Reporting System</a></li>
      </ul>
    </div>
  </nav>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var elems = document.querySelectorAll('.sidenav');
        var instances = M.Sidenav.init(elems);
    });
  </script>
