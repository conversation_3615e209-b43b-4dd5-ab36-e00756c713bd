<?php
// Setup script for email system
require 'db_connect.php';

echo "<h2>Email System Setup</h2>";

// Create email_logs table
$sql = "CREATE TABLE IF NOT EXISTS email_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    to_email VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    body TEXT NOT NULL,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('sent', 'failed') DEFAULT 'sent',
    template_used VARCHAR(100) DEFAULT NULL,
    error_message TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_sent_at (sent_at),
    INDEX idx_status (status),
    INDEX idx_template_used (template_used)
)";

if ($conn->query($sql) === TRUE) {
    echo "<p style='color: green;'>✓ Email logs table created successfully</p>";
} else {
    echo "<p style='color: red;'>✗ Error creating email logs table: " . $conn->error . "</p>";
}

// Check if users table exists
$result = $conn->query("SHOW TABLES LIKE 'users'");
if ($result->num_rows > 0) {
    echo "<p style='color: green;'>✓ Users table exists</p>";
    
    // Try to add foreign key constraint (if it doesn't exist)
    $fk_sql = "ALTER TABLE email_logs ADD CONSTRAINT fk_email_logs_user_id 
               FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE";
    
    if ($conn->query($fk_sql) === TRUE) {
        echo "<p style='color: green;'>✓ Foreign key constraint added</p>";
    } else {
        if (strpos($conn->error, 'Duplicate key name') !== false) {
            echo "<p style='color: orange;'>⚠ Foreign key constraint already exists</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Could not add foreign key constraint: " . $conn->error . "</p>";
        }
    }
} else {
    echo "<p style='color: orange;'>⚠ Users table not found - foreign key constraint not added</p>";
}

// Check PHP mail configuration
echo "<h3>PHP Mail Configuration Check</h3>";

if (function_exists('mail')) {
    echo "<p style='color: green;'>✓ PHP mail() function is available</p>";
} else {
    echo "<p style='color: red;'>✗ PHP mail() function is not available</p>";
}

// Check if sendmail or SMTP is configured
$sendmail_path = ini_get('sendmail_path');
$smtp_host = ini_get('SMTP');

if (!empty($sendmail_path)) {
    echo "<p style='color: green;'>✓ Sendmail path configured: $sendmail_path</p>";
} elseif (!empty($smtp_host)) {
    echo "<p style='color: green;'>✓ SMTP host configured: $smtp_host</p>";
} else {
    echo "<p style='color: orange;'>⚠ No mail transport configured. You may need to configure SMTP or sendmail</p>";
}

echo "<h3>Next Steps</h3>";
echo "<ol>";
echo "<li>Update email credentials in <code>email_config.php</code></li>";
echo "<li>For Gmail SMTP:";
echo "<ul>";
echo "<li>Enable 2-factor authentication on your Gmail account</li>";
echo "<li>Generate an App Password for this application</li>";
echo "<li>Update SMTP_USERNAME and SMTP_PASSWORD in email_config.php</li>";
echo "<li>Set USE_SMTP to true in email_config.php</li>";
echo "</ul>";
echo "</li>";
echo "<li>Test the email functionality using <a href='send_email_advanced.php'>send_email_advanced.php</a></li>";
echo "</ol>";

echo "<h3>Installation Complete!</h3>";
echo "<p><a href='send_email_advanced.php' style='background: #2196f3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Go to Email System</a></p>";

$conn->close();
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background-color: #f5f5f5;
}
h2, h3 {
    color: #333;
}
code {
    background-color: #e8e8e8;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}
ul, ol {
    margin-left: 20px;
}
</style>
