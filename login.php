 
<?php
require 'db_connect.php';
ob_start();
session_start();

// Redirect if already logged in
if (isset($_SESSION['barangay'])) {
    header('Location: index.php');
    exit;
}

$message = "";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = trim($_POST['username']);
    $password = $_POST['password'];

    // Prepare SQL query
    $stmt = $conn->prepare("SELECT id, password, email, barangay, role, status FROM users WHERE username = ?");
    if ($stmt) {
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $stmt->store_result();

        if ($stmt->num_rows === 1) {
            $stmt->bind_result($id, $hash, $email, $barangay, $role, $status);
            $stmt->fetch();

            if (strtolower($status) === 'pending') {
                $message = "Pending user. Please wait for approval.";
            } elseif (password_verify($password, $hash)) {
                // Login success
                $_SESSION['user_id'] = $id;
                $_SESSION['role'] = $role;
                $_SESSION['barangay'] = $barangay;
                $_SESSION['email'] = $email;
                header("Location: index.php");
                exit;
            } else {
                $message = "Invalid credentials.";
            }
        } else {
            $message = "Invalid credentials.";
        }

        $stmt->close();
    } else {
        $message = "Database error: Unable to prepare statement.";
    }
}
?>


<?php include 'header.php'; ?>
<style>
   

        .card {
            border: none;
           
            background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
            box-shadow: 0 25px 50px rgba(0,0,0,0.1),
                        0 15px 35px rgba(0,0,0,0.05),
                        0 5px 15px rgba(0,0,0,0.03);
        
            overflow: hidden;
            position: relative;
           
            border: 1px solid rgba(255,255,255,0.3);
        }

      

      

        .card-content {
            padding: 3rem 2.5rem !important;
            background: rgba(255,255,255,0.95);
            position: relative;
        }

        .page-title {
            font-size: 29.1px;
            color: #212121;
            
            margin-left: 0;
             
          
            margin-bottom: 2rem;
        }

        .input-field {
            margin-bottom: 2rem;
            position: relative;
        }

        .input-field input {
            border-bottom: 2px solid #e0e0e0;
            padding: 1rem 0 0.5rem 0;
            font-size: 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: transparent;
        }

    

        .input-field label {
            color: #757575 !important;
            font-size: 1rem;
            font-weight: 400;
            transition: all 0.3s ease;
        }

      

      

        .input-field .prefix.active {
            color: #0097a7;
          
        }

     

       

    

      

        .btn i {
            font-size: 1.2rem;
            
        }

        .btn:hover {
            background: linear-gradient(135deg, #26c6da 0%, #00acc1 100%) !important;
          
          
        }

        .btn:active {
             
            box-shadow: 0 6px 15px rgba(0,172,193,0.25);
        }

     
      

       
  

        .error-message {
           
            color: #c62828;
            font-size: 0.9rem;
           padding-top:10px;
           
             
        }

        @keyframes slideInError {
            from {
                opacity: 0;
             
            }
            to {
                opacity: 1;
                 
            }
        }

        .error-message i {
            font-size: 1.25rem;
            color: #f44336;
            
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .logo-container img {
            
            border-radius: 50%;
            
            
        }

     

        /* Enhanced Container */
        .container {
            position: relative;
        }

     

        /* Enhanced Page Title */
        .page-title {
            font-size: 1.75rem;
            color: #37474f;
            margin-bottom: 2.5rem;
            text-align: center;
            font-weight: 300;
            letter-spacing: 0.5px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

    

        /* Enhanced Responsive Design */
        @media screen and (max-width: 600px) {
            .card-content {
                padding: 2rem 1.5rem !important;
            }

            .page-title {
                font-size: 1.5rem;
            }

         
            .logo-container {
                display: none;
            }

            .input-field {
                margin-bottom: 1.5rem;
            }

         
            .register-link {
                padding: 1rem;
                font-size: 0.875rem;
            }
        }

        @media screen and (max-width: 400px) {
            .card {
                margin: 1rem 0.5rem;
                border-radius: 16px;
            }

            .card-content {
                padding: 1.5rem 1rem !important;
            }
        }
</style>
 <div class="container">
   
        <div class="row ">
 
        <div class="col  s12 m5 push-m4">
       
        <div class="card z-depth-0" style="border:1px solid #ddd;">
            
                    <div class="card-content">
<h5>Login</h5>
<form action="#" method="post">
  <div class="input-field">
    <input id="username" name="username" type="text" required>
    <label for="username">Username</label>
  </div>
  <div class="input-field">
    <input id="password" name="password" type="password" required>
    <label for="password">Password</label>
  </div>
  <button class="btn blue waves-effect" type="submit">Login</button>
</form>
<p class="error-message"><?php echo $message; ?></p>
  </div>
    </div>
      </div>
        </div>
          </div>
<?php include 'footer.php'; ?>
