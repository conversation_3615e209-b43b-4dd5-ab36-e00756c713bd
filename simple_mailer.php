<?php
// Simple SMTP Mailer for XAMPP without requiring PHP<PERSON>ailer installation
// This is a basic SMTP implementation for sending emails via Gmail

class SimpleMailer {
    private $smtp_host;
    private $smtp_port;
    private $smtp_username;
    private $smtp_password;
    private $smtp_secure;
    private $from_email;
    private $from_name;
    
    public function __construct($config = []) {
        $this->smtp_host = $config['host'] ?? 'smtp.gmail.com';
        $this->smtp_port = $config['port'] ?? 587;
        $this->smtp_username = $config['username'] ?? '';
        $this->smtp_password = $config['password'] ?? '';
        $this->smtp_secure = $config['secure'] ?? 'tls';
        $this->from_email = $config['from_email'] ?? '';
        $this->from_name = $config['from_name'] ?? 'Disaster Watch System';
    }
    
    public function sendEmail($to_email, $subject, $body, $from_name = null) {
        if (empty($this->smtp_username) || empty($this->smtp_password)) {
            return ['success' => false, 'message' => 'SMTP credentials not configured'];
        }
        
        if ($this->smtp_username === '<EMAIL>' || $this->smtp_password === 'your-app-password') {
            return ['success' => false, 'message' => 'Please update SMTP credentials in email_config.php'];
        }
        
        $from_name = $from_name ?: $this->from_name;
        
        try {
            // Create socket connection
            $socket = $this->createConnection();
            if (!$socket) {
                return ['success' => false, 'message' => 'Could not connect to SMTP server'];
            }
            
            // SMTP conversation
            $this->readResponse($socket); // Read initial response
            
            // EHLO
            fwrite($socket, "EHLO localhost\r\n");
            $this->readResponse($socket);
            
            // STARTTLS
            if ($this->smtp_secure === 'tls') {
                fwrite($socket, "STARTTLS\r\n");
                $this->readResponse($socket);
                
                if (!stream_socket_enable_crypto($socket, true, STREAM_CRYPTO_METHOD_TLS_CLIENT)) {
                    fclose($socket);
                    return ['success' => false, 'message' => 'TLS encryption failed'];
                }
                
                // EHLO again after TLS
                fwrite($socket, "EHLO localhost\r\n");
                $this->readResponse($socket);
            }
            
            // AUTH LOGIN
            fwrite($socket, "AUTH LOGIN\r\n");
            $this->readResponse($socket);
            
            fwrite($socket, base64_encode($this->smtp_username) . "\r\n");
            $this->readResponse($socket);
            
            fwrite($socket, base64_encode($this->smtp_password) . "\r\n");
            $response = $this->readResponse($socket);
            
            if (strpos($response, '235') === false) {
                fclose($socket);
                return ['success' => false, 'message' => 'SMTP authentication failed. Check your credentials.'];
            }
            
            // MAIL FROM
            fwrite($socket, "MAIL FROM: <{$this->from_email}>\r\n");
            $this->readResponse($socket);
            
            // RCPT TO
            fwrite($socket, "RCPT TO: <{$to_email}>\r\n");
            $this->readResponse($socket);
            
            // DATA
            fwrite($socket, "DATA\r\n");
            $this->readResponse($socket);
            
            // Email headers and body
            $email_data = $this->buildEmailData($to_email, $subject, $body, $from_name);
            fwrite($socket, $email_data . "\r\n.\r\n");
            $this->readResponse($socket);
            
            // QUIT
            fwrite($socket, "QUIT\r\n");
            fclose($socket);
            
            return ['success' => true, 'message' => 'Email sent successfully via SMTP'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'SMTP Error: ' . $e->getMessage()];
        }
    }
    
    private function createConnection() {
        $context = stream_context_create([
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            ]
        ]);
        
        $socket = stream_socket_client(
            "{$this->smtp_host}:{$this->smtp_port}",
            $errno,
            $errstr,
            30,
            STREAM_CLIENT_CONNECT,
            $context
        );
        
        if (!$socket) {
            error_log("SMTP Connection failed: $errstr ($errno)");
            return false;
        }
        
        return $socket;
    }
    
    private function readResponse($socket) {
        $response = '';
        while (($line = fgets($socket, 515)) !== false) {
            $response .= $line;
            if (substr($line, 3, 1) === ' ') {
                break;
            }
        }
        return $response;
    }
    
    private function buildEmailData($to_email, $subject, $body, $from_name) {
        $boundary = md5(time());
        
        $headers = [
            "From: {$from_name} <{$this->from_email}>",
            "To: {$to_email}",
            "Subject: {$subject}",
            "MIME-Version: 1.0",
            "Content-Type: multipart/alternative; boundary=\"{$boundary}\"",
            "Date: " . date('r'),
            "Message-ID: <" . md5(time()) . "@{$this->smtp_host}>"
        ];
        
        $email_data = implode("\r\n", $headers) . "\r\n\r\n";
        
        // Plain text version
        $email_data .= "--{$boundary}\r\n";
        $email_data .= "Content-Type: text/plain; charset=UTF-8\r\n";
        $email_data .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
        $email_data .= strip_tags($body) . "\r\n\r\n";
        
        // HTML version
        $email_data .= "--{$boundary}\r\n";
        $email_data .= "Content-Type: text/html; charset=UTF-8\r\n";
        $email_data .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
        $email_data .= $this->formatEmailBody($subject, $body, $from_name) . "\r\n\r\n";
        
        $email_data .= "--{$boundary}--\r\n";
        
        return $email_data;
    }
    
    private function formatEmailBody($subject, $body, $from_name) {
        return "
        <html>
        <head>
            <title>{$subject}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #1976d2; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }
                .content { background-color: #f9f9f9; padding: 20px; border-radius: 0 0 5px 5px; margin-bottom: 20px; }
                .footer { color: #666; font-size: 12px; text-align: center; margin-top: 20px; border-top: 1px solid #ddd; padding-top: 10px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>{$subject}</h2>
                </div>
                <div class='content'>
                    " . nl2br(htmlspecialchars($body)) . "
                </div>
                <div class='footer'>
                    <p>
                        This email was sent from the Disaster Watch System.<br>
                        Sent by: " . htmlspecialchars($from_name) . "<br>
                        Date: " . date('Y-m-d H:i:s') . "
                    </p>
                </div>
            </div>
        </body>
        </html>";
    }
}

// Function to send email using SimpleMailer
function sendEmailSimple($to_email, $subject, $body, $from_name = '') {
    $config = [
        'host' => SMTP_HOST,
        'port' => SMTP_PORT,
        'username' => SMTP_USERNAME,
        'password' => SMTP_PASSWORD,
        'secure' => SMTP_SECURE,
        'from_email' => SMTP_FROM_EMAIL,
        'from_name' => SMTP_FROM_NAME
    ];
    
    $mailer = new SimpleMailer($config);
    return $mailer->sendEmail($to_email, $subject, $body, $from_name);
}
?>
