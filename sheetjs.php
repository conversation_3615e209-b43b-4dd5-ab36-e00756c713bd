 
 

<?php
require 'db_connect.php';
session_start();

if (!isset($_SESSION['user_id'])) {
  header('Location: login.php');
  exit;
}

$userBarangay = $_SESSION['barangay'] ?? '';
$userRole = $_SESSION['role'] ?? '';
 
date_default_timezone_set('Asia/Manila'); // Set your default TZ to Asia/Manila

// Get evacuation reports data based on user role
if ($userRole === 'admin') {
    $sql = "SELECT * FROM evacuation_reports ORDER BY id DESC";
    $stmt = $conn->prepare($sql);
} else {
    $sql = "SELECT * FROM evacuation_reports WHERE barangay = ? ORDER BY id DESC";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("s", $userBarangay);
}

$stmt->execute();

if ($stmt->error) {
    die("SQL Error: " . $stmt->error);
}
$result = $stmt->get_result();

$data = [];
while ($row = $result->fetch_assoc()) {
    $data[] = $row;
}
 
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Export Evacuation Data to Excel</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"/>
  <script src="https://cdn.sheetjs.com/xlsx-0.20.3/package/dist/xlsx.full.min.js"></script>
  <?php include 'header.php'; ?>
</head>
<body class="grey lighten-4">
  
  <div class="container" style="margin-top: 10px;padding:10px;">
    <div class="row">
      <div class="col s12">
        <div class="card ">
          <div class="card-content">
            <h5 style="margin-bottom: 20px;margin-left:14px;font-size:18px;" class="blue-text text-darken-3">
              <i class="material-icons left" style="font-size:22px;">file_download</i>
              Export Excel  <?php echo $userRole === 'admin' ? '' : $userBarangay; ?>
            </h5>
            
            <form id="exportForm" method="post" class="" style="padding: 20px;">
              <div class="row ">
                <div class="col s12 m6">
                  <div class="input-field">
                    <input type="date" name="fromDate" id="fromDate" class="validate">
                    <label for="fromDate" class="active">From Date:</label>
                  </div>
                </div>
                <div class="col s12 m6">
                  <div class="input-field">
                    <input type="date" name="toDate" id="toDate" class="validate">
                    <label for="toDate" class="active">To Date:</label>
                  </div>
                </div>
              </div>
              
              <div class="row">
                <div class="col s12">
                  <button style="font-weight:500;font-size:13.4px;" type="button" class="btn green darken-3 waves-effect waves-light" onclick="requestPasswordForExcel()">
                    <i class="material-icons left">lock</i>Download Excel 
                   <i> <span class="yellow-text" style="font-size:12.4px;font-weight:lighter;">(Password Required)</span></i>
                  </button>

                  <button style="font-weight:500;font-size:13.4px;" type="reset" class="btn orange darken-1 waves-effect waves-light" id="resetBtn">
                    <i class="material-icons left">refresh</i> Reset
                  </button>
                </div>
              </div>
              
              <div id="recordCount" class="card-panel blue lighten-5" style="margin-top: 20px; display: none;"></div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Password Modal for Excel Download -->
  <div id="excelPasswordModal" class="modal">
    <div class="modal-content">
      <h5 style="font-size:24.2px;" class="blue-grey-text">
        <i class="material-icons left">lock</i>Password Required for Excel Download
      </h5>
      
      <div class="card-panel teal lighten-5" style="border-left: 4px solid #26a69a;">
        <p style="margin: 0; color: #009688; font-weight: 500;">
          <i class="material-icons left tiny">warning</i>
          <strong>Enter your password first</strong> - Excel download will not proceed without correct credentials
        </p>
      </div>

      <div class="row">
        <div class="col s12">
          <div class="input-field"> 
            <input type="text" id="excelUsername" class="validate">
            <label for="excelUsername">Username or Email</label>
            <span class="helper-text">Enter your username or email address</span>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col s12">
          <div class="input-field">
            <input type="password" id="excelPassword" class="validate" required>
            <label for="excelPassword">Password</label>
            <span class="helper-text">Enter your password to proceed with Excel download</span>
          </div>
        </div>
      </div>

      <div id="excelPasswordError" style="display: none;" class="card-panel red lighten-4">
        <i class="material-icons left">error</i>
        <span id="excelPasswordErrorText">Wrong password - Excel download blocked. Data will not proceed.</span>
      </div>

      <div id="excelPasswordSuccess" style="display: none;" class="card-panel green lighten-4">
        <i class="material-icons left">check_circle</i>
        <span>Password verified! Proceeding with Excel download...</span>
      </div>
    </div>

    <div class="modal-footer">
      <button class="modal-close btn red lighten-1 waves-effect waves-light">
        <i class="material-icons left">cancel</i>Cancel
      </button>
      <button id="confirmExcelPasswordBtn" class="btn blue waves-effect waves-light">
        <i class="material-icons left">check</i>Verify Password
      </button>
    </div>
  </div>

  <script>
    let isExcelPasswordVerified = false;
    let excelPasswordModal;

    document.addEventListener('DOMContentLoaded', function() {
      // Initialize Materialize components
      M.AutoInit();

      // Initialize password modal
      excelPasswordModal = M.Modal.init(document.getElementById('excelPasswordModal'), {
        dismissible: true,
        opacity: 0.5,
        inDuration: 300,
        outDuration: 200
      });

      // Add event listener for password confirmation
      document.getElementById('confirmExcelPasswordBtn').addEventListener('click', function(e) {
        e.preventDefault();
        verifyExcelPassword();
      });

      // Add enter key support for password fields
      document.getElementById('excelPassword').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          verifyExcelPassword();
        }
      });
      
      // Add event listener for reset button
      document.getElementById('resetBtn').addEventListener('click', function(e) {
        e.preventDefault();
        document.getElementById('fromDate').value = '';
        document.getElementById('toDate').value = '';
        updateRecordCount();
      });

      // Initial record count update
      updateRecordCount();
    });

    function updateRecordCount() {
      const fromDate = document.getElementById('fromDate').value || '';
      const toDate = document.getElementById('toDate').value || '';
      
      fetch('get_nip_data.php?count=true&fromDate=' + fromDate + '&toDate=' + toDate)
        .then(response => response.json())
        .then(data => {
          if (data.error) {
            document.getElementById('recordCount').innerHTML = 
              `<div class="red-text">Error: ${data.error}</div>`;
          } else {
            document.getElementById('recordCount').innerHTML = 
              `<div class="blue-text text-darken-2">
                <i class="material-icons left">info</i>
                Records found: <b>${data.count}</b> for the selected date range
                ${fromDate && toDate ? 
                  `<br><small>(${new Date(fromDate).toLocaleDateString()} to ${new Date(toDate).toLocaleDateString()})</small>` 
                  : '<br><small>(All dates)</small>'
                }
              </div>`;
          }
          document.getElementById('recordCount').style.display = 'block';
        })
        .catch(error => {
          console.error('Error:', error);
          document.getElementById('recordCount').innerHTML = 
            '<div class="red-text">Error loading record count</div>';
          document.getElementById('recordCount').style.display = 'block';
        });
    }

    function requestPasswordForExcel() {
      // Clear previous states
      document.getElementById('excelUsername').value = '';
      document.getElementById('excelPassword').value = '';
      document.getElementById('excelPasswordError').style.display = 'none';
      document.getElementById('excelPasswordSuccess').style.display = 'none';

      // Show explicit message about password requirement
      M.toast({
        html: '<i class="material-icons left">lock</i>Enter your password first before Excel download',
        classes: 'amber darken-4',
        displayLength: 4000
      });

      // Open password modal
      excelPasswordModal.open();

      // Focus on username input
      setTimeout(() => {
        document.getElementById('excelUsername').focus();
      }, 300);

      console.log('Excel download requested - password verification required');
    }

    async function verifyExcelPassword() {
      const username = document.getElementById('excelUsername').value.trim();
      const password = document.getElementById('excelPassword').value.trim();
      const passwordError = document.getElementById('excelPasswordError');
      const passwordSuccess = document.getElementById('excelPasswordSuccess');
      const confirmBtn = document.getElementById('confirmExcelPasswordBtn');

      // Clear previous states
      passwordError.style.display = 'none';
      passwordSuccess.style.display = 'none';

      if (!username || !password) {
        passwordError.style.display = 'block';
        document.getElementById('excelPasswordErrorText').textContent = 'Username and password required - Excel download blocked';

        M.toast({
          html: '<i class="material-icons left">block</i>Enter your credentials - Excel download blocked',
          classes: 'red darken-2',
          displayLength: 4000
        });
        return;
      }

      // Show loading state
      confirmBtn.disabled = true;
      confirmBtn.innerHTML = '<i class="material-icons left">hourglass_empty</i>Verifying Password...';

      console.log('Verifying Excel download credentials - access currently blocked');

      try {
        const formData = new FormData();
        formData.append('username', username);
        formData.append('password', password);

        const response = await fetch('verify_excel_password.php', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        if (result.success) {
          // Password CORRECT - Show success and proceed
          isExcelPasswordVerified = true;
          passwordSuccess.style.display = 'block';
          console.log('Excel password verified successfully - download access granted');

          M.toast({
            html: '<i class="material-icons left">check_circle</i>Password verified! Proceeding with Excel download...',
            classes: 'green darken-1',
            displayLength: 3000
          });

          // Wait a moment to show success message
          setTimeout(() => {
            excelPasswordModal.close();
            // Execute Excel download ONLY after successful verification
            exportToExcel();
          }, 1500);

        } else {
          // Password WRONG - Block download and show error
          isExcelPasswordVerified = false;
          passwordError.style.display = 'block';
          document.getElementById('excelPasswordErrorText').textContent = 'Wrong password - Excel download blocked. Data will not proceed.';
          document.getElementById('excelUsername').value = '';
          document.getElementById('excelPassword').value = '';
          document.getElementById('excelUsername').focus();

          console.log('Excel password verification failed - download access DENIED');

          M.toast({
            html: '<i class="material-icons left">block</i>Wrong password - Excel download blocked!',
            classes: 'red darken-2',
            displayLength: 5000
          });
        }
      } catch (error) {
        // Connection error - Block download
        isExcelPasswordVerified = false;
        passwordError.style.display = 'block';
        document.getElementById('excelPasswordErrorText').textContent = 'Connection error - Excel download blocked until password verified.';

        console.log('Excel password verification error - download access BLOCKED');

        M.toast({
          html: '<i class="material-icons left">error</i>Connection error - Excel download blocked',
          classes: 'red darken-2',
          displayLength: 4000
        });
      } finally {
        // Reset button state
        confirmBtn.disabled = false;
        confirmBtn.innerHTML = '<i class="material-icons left">check</i>Verify Password';
      }
    }

    async function exportToExcel() {
      // Security check - this method should only be called after password verification
      if (!isExcelPasswordVerified) {
        M.toast({
          html: '<i class="material-icons left">block</i>Password verification required - Excel download blocked',
          classes: 'red darken-2',
          displayLength: 4000
        });
        return;
      }

      console.log('Excel download initiated - password verification completed');

      const fromDate = document.getElementById('fromDate').value;
      const toDate = document.getElementById('toDate').value;
      
      // Show loading indicator
      document.getElementById('recordCount').innerHTML = 
        '<div class="progress"><div class="indeterminate"></div></div>';
      
      try {
        const response = await fetch('get_nip_data.php?fromDate=' + fromDate + '&toDate=' + toDate);
        const data = await response.json();

        if (data.error) {
          document.getElementById('recordCount').innerHTML = 
            `<div class="red-text">Error: ${data.error}</div>`;
          return;
        }

        if (data.length === 0) {
          document.getElementById('recordCount').innerHTML = 
            '<div class="orange-text">No data found for the selected date range</div>';
          return;
        }

        // Format data for Excel export
        const formatted = data.map(row => ({
          "#": row.id,
          "Activity Date": row.activity_date,
          "Activity Time": row.activity_time,
          "Barangay": row.barangay,
          "Geologic Events": row.GEOLOGIC,
          "Weather Events": row.WEATHER,
          "Man-made Events": row.MANMADE,
          "Affected Areas": row.affected_areas,
          "Individuals Affected": row.individuals_affected,
          "Families Affected": row.families_affected,
          "Evacuation Site": row.evacuation_site,
          "Evacuated Individuals": row.evac_individuals,
          "Evacuated Families": row.evac_families,
          "Male Evacuees": row.evac_male,
          "Female Evacuees": row.evac_female,
          "Pregnant Evacuees": row.evac_pregnant,
          "PWD Evacuees": row.evac_pwd,
          "Under 5 years": row.evac_under_5,
          "5-17 years": row.evac_5_to_17,
          "18-59 years": row.evac_18_to_59,
          "60+ years": row.evac_60_above,
          "Casualties": row.no_of_casualties,
          "Treated on Site": row.no_of_treated_on_site,
          "Brought to Hospital": row.no_of_brought_to_hospital,
          "Deaths": row.no_of_deaths,
          "Missing": row.no_of_missing,
          "Additional Info": row.additional_info,
          "CAUSES 1": row.cause_1_d,
          "(C1)  NO. OF 0-15 yrs": row.no_of_zero_to_fifteen_d,
          "(C1)  NO. OF >15 yrs": row.no_of_greater_than_fifteen_d,
          "Total (Causes 1)": row.total_cause_1_d,

          "CAUSES 2": row.cause_2_d,
          "(C2)  NO. OF 0-15 yrs": row.no_of_zero_to_fifteen_d_c2,
          "(C2)  NO. OF >15 yrs":row.no_of_greater_than_fifteen_d_c2,
          "Total (Causes 2)":row.total_cause_2_d,
          "CAUSES 3":row.cause_3_d,
          "(C3)  NO. OF 0-15 yrs":row.no_of_zero_to_fifteen_d_c3,
          "(C3)  NO. OF >15 yrs":row.no_of_greater_than_fifteen_d_c3,
          "Total (Causes 3)":row.total_cause_3_d,
          "CAUSES 4":row.cause_4_d,
          "(C4)  NO. OF 0-15 yrs":row.no_of_zero_to_fifteen_d_c4,
          "(C4)  NO. OF >15 yrs":row.no_of_greater_than_fifteen_d_c4,
          "Total (Causes 4)":row.total_cause_4_d,
          "CAUSES 5":row.cause_5_d,
          "(C5)  NO. OF 0-15 yrs":row.no_of_zero_to_fifteen_d_c5,
          "(C5)  NO. OF >15 yrs":row.no_of_greater_than_fifteen_d_c5,
          "Total (Causes 5)":row.total_cause_5_d,
          "Causes 1":row.causes_1_top_five,
          "(C1)  NO. OF 0-15 yrs":row.no_of_zero_to_fifteen_d_c1_top_five,
          "(C1)  NO. OF  >15 yrs":row.no_of_greater_than_fifteen_d_c1_top_five,
          "Total (Causes 1)":row.total_cause_1_top_five_top_five,
          "Causes 2":row.causes_2_top_five,
          "(C2)  NO. OF 0-15 yrs":row.no_of_zero_to_fifteen_d_c2_top_five,
          "(C2)  NO. OF  >15 yrs":row.no_of_greater_than_fifteen_d_c2_top_five,
          "Total (Causes 2)":row.total_cause_2_top_five,
          "Causes 3":row.causes_3_top_five,
          "(C3)  NO. OF 0-15 yrs":row.no_of_zero_to_fifteen_d_c3_top_five,
          "(C3)  NO. OF  >15 yrs":row.no_of_greater_than_fifteen_d_c3_top_five,
          "Total (Causes 3)":row.total_cause_3_top_five,
          "Causes 4":row.causes_4_top_five,
          "(C4)  NO. OF 0-15 yrs":row.no_of_zero_to_fifteen_d_c4_top_five,
          "(C4)  NO. OF  >15 yrs":row.no_of_greater_than_fifteen_d_c4_top_five,
          "Total (Causes 4)":row.total_cause_4_top_five,
          "Causes 5":row.causes_5_top_five,
          "(C5)  NO. OF 0-15 yrs":row.no_of_zero_to_fifteen_d_c5_top_five,
          "(C5)  NO. OF  >15 yrs":row.no_of_greater_than_fifteen_d_c5_top_five,
          "Total (Causes 5)":row.total_cause_5_top_five,
          "Service 1":row.service_1_services_provided,
          "Service 2":row.service_2_services_provided,
          "Service 3":row.service_3_services_provided,
          "Service 4":row.service_4_services_provided,
          "Service 5":row.service_5_services_provided,
          "Essential Drug 1":row.essential_drug_1,
          "(ED1) Stock Quantity":row.stock_quantity_ed1,
          "(ED1)  Stock quantity Remaining":row.stock_quantity_remaining_ed1,
          "Essential Drug 2":row.essential_drug_2,
          "(ED2) Quantity Given":row.quantity_given_ed2,
          "(ED2) Stock quantity Remaining":row.stock_quantity_remaining_ed2,
          "Essential Drug 3":row.essential_drug_3,
          "(ED3) Quantity Given":row.quantity_given_ed3,
          "(ED3)  Stock quantity Remaining":row.stock_quantity_remaining_ed3,
          "Essential Drug 4":row.essential_drug_4,
          "(ED4) Quantity Given":row.quantity_given_ed4,
          "(ED4)  Stock quantity Remaining":row.stock_quantity_remaining_ed4,
          "Essential Drug 5":row.essential_drug_5,
          "(ED5) Quantity Given":row.quantity_given_ed5,
          "(ED5)  Stock quantity Remaining":row.stock_quantity_remaining_ed5,
          "Communication":row.communication,
          "Remarks":row.remarks_communication,
          "Electric Power":row.electric_power,
          "Remarks":row.remarks_electric_power,
          "water":row.water,
          "Remarks":row.remarks_water,
          "Roads/Bridges":row.roads_bridges,
          "Remarks":row.remarks_road_bridges,
          "Other":row.Other,
          "Remarks":row.remarks_other,
          "Problems Encountered":row.problems_encountered,
          "Recommendations":row.recommendations,
          "(ED1) Quantity Given":row.quantity_given_ed1,
          "(ED2) Stock Quantit":row.stock_quantity_ed2,
          "(ED3) Stock Quantity":row.stock_quantity_ed3,
          "(ED4) Stock Quantity":row.stock_quantity_ed4,
          "(ED5) Stock Quantity":row.stock_quantity_ed5,
          "Service 1 - Number of Patient":row.service_1_no_of_patient,
          "Service 2 - Number of Patient":row.service_2_no_of_patient,
          "Service 3 - Number of Patient":row.service_3_no_of_patient,
          "Service 4 - Number of Patient":row.service_4_no_of_patient,
          "Service 5 - Number of Patient":row.service_5_no_of_patient,
          "Date Prepared":row.date_prepared,
          "Mobile Number":row.mobile_no,
          "Printed Name":row.printed_name,
          "Email":row.email,
          "Designation/Office:":row.designation_office
        }));

        // Create workbook and worksheet
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(formatted);
        
        // Set column widths
        const colWidths = [];
        for (let i = 0; i < Object.keys(formatted[0]).length; i++) {
          colWidths.push({ wch: 20 }); // Default width of 20 characters
        }
        ws['!cols'] = colWidths;
        
        // Add the worksheet to the workbook
        XLSX.utils.book_append_sheet(wb, ws, "Evacuation Reports");
        
        // Format dates for filename
        const formattedFromDate = fromDate ? fromDate.replace(/-/g, '') : 'all';
        const formattedToDate = toDate ? toDate.replace(/-/g, '') : 'dates';
        
        // Generate filename with date range and user info
        const userInfo = "<?php echo $userRole === 'admin' ? 'All_Barangays' : $userBarangay; ?>";
        const filename = `Evacuation_Reports_${userInfo}_${formattedFromDate}_to_${formattedToDate}.xlsx`;
        
        // Write the workbook and trigger download
        XLSX.writeFile(wb, filename);
        
        console.log(`Excel download completed: ${filename} - Password verified`);

        // Update record count with success message
        document.getElementById('recordCount').innerHTML =
          `<div class="green-text text-darken-2">
            <i class="material-icons left">check_circle</i>
            Successfully exported ${data.length} records to Excel - Password verified
          </div>`;

        // Reset password verification for next download
        isExcelPasswordVerified = false;

      } catch (error) {
        console.error("Error exporting data:", error);
        document.getElementById('recordCount').innerHTML =
          '<div class="red-text">An error occurred while exporting data. Please try again.</div>';

        // Reset password verification on error
        isExcelPasswordVerified = false;
      }
    }

    // Add event listeners for date changes
    document.getElementById('fromDate').addEventListener('change', updateRecordCount);
    document.getElementById('toDate').addEventListener('change', updateRecordCount);
  </script>

</body>
</html>
