<?php
require 'db_connect.php';
session_start();

if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    exit('Not authenticated');
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['records']) || !isset($input['fields'])) {
    http_response_code(400);
    exit('Invalid input data');
}

$records = $input['records'];
$selectedFields = $input['fields'];
$filters = $input['filters'] ?? [];

// Field labels mapping
$fieldLabels = [
    'id' => 'Record ID',
    'activity_date' => 'Activity Date',
    'activity_time' => 'Activity Time',
    'barangay' => 'Barangay',
    'GEOLOGIC' => 'Geologic Events',
    'WEATHER' => 'Weather Events',
    'MANMADE' => 'Man-made Events',
    'affected_areas' => 'Affected Areas',
    'individuals_affected' => 'Individuals Affected',
    'families_affected' => 'Families Affected',
    'evacuation_site' => 'Evacuation Site',
    'evac_individuals' => 'Evacuated Individuals',
    'evac_families' => 'Evacuated Families',
    'evac_male' => 'Male Evacuees',
    'evac_female' => 'Female Evacuees',
    'evac_pregnant' => 'Pregnant Evacuees',
    'evac_pwd' => 'PWD Evacuees',
    'evac_under_5' => 'Under 5 years',
    'evac_5_to_17' => '5-17 years',
    'evac_18_to_59' => '18-59 years',
    'evac_60_above' => '60+ years',
    'no_of_casualties' => 'Number of Casualties',
    'no_of_treated_on_site' => 'Treated on Site',
    'no_of_brought_to_hospital' => 'Brought to Hospital',
    'no_of_deaths' => 'Number of Deaths',
    'no_of_missing' => 'Number Missing',
    'additional_info' => 'Additional Information',
    'problems_encountered' => 'Problems Encountered',
    'recommendations' => 'Recommendations'
];

// Create Excel content
$filename = 'evacuation_reports_' . date('Y-m-d_H-i-s') . '.xlsx';

// Set headers for Excel download
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: max-age=0');

// Simple Excel XML format
echo '<?xml version="1.0"?>';
echo '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40">';

// Styles
echo '<Styles>';
echo '<Style ss:ID="Header">';
echo '<Font ss:Bold="1"/>';
echo '<Interior ss:Color="#4CAF50" ss:Pattern="Solid"/>';
echo '</Style>';
echo '</Styles>';

echo '<Worksheet ss:Name="Evacuation Reports">';
echo '<Table>';

// Header row
echo '<Row>';
foreach ($selectedFields as $field) {
    $label = $fieldLabels[$field] ?? $field;
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">' . htmlspecialchars($label) . '</Data></Cell>';
}
echo '</Row>';

// Data rows
foreach ($records as $record) {
    echo '<Row>';
    foreach ($selectedFields as $field) {
        $value = $record[$field] ?? '';
        
        // Format specific fields
        if ($field === 'activity_date' && !empty($value)) {
            $value = date('Y-m-d', strtotime($value));
        } elseif ($field === 'activity_time' && !empty($value)) {
            $value = date('H:i', strtotime($value));
        }
        
        // Determine data type
        $dataType = 'String';
        if (is_numeric($value) && $field !== 'id') {
            $dataType = 'Number';
        }
        
        echo '<Cell><Data ss:Type="' . $dataType . '">' . htmlspecialchars($value) . '</Data></Cell>';
    }
    echo '</Row>';
}

echo '</Table>';
echo '</Worksheet>';
echo '</Workbook>';
?>