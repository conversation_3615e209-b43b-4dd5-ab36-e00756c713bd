# Email System Setup Guide

This guide will help you set up the email system to send emails to Gmail and other email providers.

## Files Created

1. **send_email.php** - Basic email sending page using PHP mail()
2. **send_email_advanced.php** - Advanced email system with templates and SMTP support
3. **email_config.php** - Configuration file for email settings
4. **setup_email_system.php** - Installation script
5. **create_email_logs_table.sql** - Database table creation script

## Quick Setup

### Step 1: Run the Setup Script
1. Open your browser and navigate to: `http://your-domain/setup_email_system.php`
2. This will create the necessary database table and check your PHP configuration

### Step 2: Configure Email Settings
1. Open `email_config.php` in a text editor
2. Update the following settings:

```php
// For Gmail SMTP
define('SMTP_USERNAME', '<EMAIL>'); // Your Gmail address
define('SMTP_PASSWORD', 'your-app-password');    // Your Gmail App Password
define('USE_SMTP', true); // Set to true to use SMTP
```

### Step 3: Gmail App Password Setup (for SMTP)
1. Go to your Google Account settings
2. Enable 2-Factor Authentication
3. Go to Security > App passwords
4. Generate a new app password for "Mail"
5. Use this app password (not your regular password) in the configuration

## Usage

### Basic Email Sending
- Navigate to `send_email.php` for a simple email form
- Fill in recipient, subject, and message
- Click "Send Email"

### Advanced Email System
- Navigate to `send_email_advanced.php` for the full-featured system
- Choose from pre-built templates or compose custom emails
- View email history and delivery status

## Email Templates

The system includes these pre-built templates:

1. **Disaster Alert** - For urgent disaster notifications
2. **Evacuation Notice** - For evacuation orders
3. **Safety Update** - For status updates
4. **All Clear** - For situation resolved notifications

## Configuration Options

### Email Methods
- **PHP mail()** - Uses server's built-in mail function (default)
- **SMTP** - Uses Gmail or other SMTP servers (recommended)

### Settings in email_config.php
```php
define('EMAIL_ENABLED', true);     // Enable/disable email sending
define('USE_SMTP', false);         // Use SMTP instead of PHP mail()
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_SECURE', 'tls');
```

## Troubleshooting

### Common Issues

1. **"Email Configuration Error"**
   - Update credentials in `email_config.php`
   - Make sure Gmail App Password is correct

2. **"Failed to send email using PHP mail()"**
   - Your server may not have mail configured
   - Try using SMTP instead by setting `USE_SMTP` to `true`

3. **"SMTP Error: Authentication failed"**
   - Check your Gmail username and app password
   - Make sure 2-factor authentication is enabled
   - Verify the app password is for "Mail"

4. **"PHPMailer not installed"**
   - The system will fall back to PHP mail()
   - To install PHPMailer: `composer require phpmailer/phpmailer`

### Server Requirements

- PHP 7.0 or higher
- MySQL/MariaDB database
- Either:
  - Working PHP mail() function, OR
  - SMTP access (Gmail, etc.)

### Testing Email Delivery

1. Start with the basic `send_email.php` page
2. Send a test email to yourself
3. Check spam folder if email doesn't arrive
4. If PHP mail() doesn't work, configure SMTP in `email_config.php`

## Security Notes

- Never commit real email credentials to version control
- Use environment variables for production:
  ```php
  define('SMTP_USERNAME', $_ENV['GMAIL_USERNAME']);
  define('SMTP_PASSWORD', $_ENV['GMAIL_APP_PASSWORD']);
  ```
- Gmail App Passwords are safer than regular passwords
- Consider using a dedicated email account for system notifications

## Database Schema

The system creates an `email_logs` table to track sent emails:

```sql
CREATE TABLE email_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    to_email VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    body TEXT NOT NULL,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('sent', 'failed') DEFAULT 'sent',
    template_used VARCHAR(100) DEFAULT NULL,
    error_message TEXT DEFAULT NULL
);
```

## Features

### Basic Email System (send_email.php)
- Simple email form
- HTML email formatting
- Email logging
- Basic templates

### Advanced Email System (send_email_advanced.php)
- Pre-built disaster response templates
- SMTP support for reliable delivery
- Email history with status tracking
- Template management
- Error logging
- Responsive design

## Support

If you encounter issues:
1. Check the setup script output for configuration problems
2. Verify your Gmail settings and app password
3. Test with a simple email first
4. Check server error logs for PHP mail issues

## Next Steps

After setup:
1. Test email delivery with both systems
2. Customize email templates for your needs
3. Add the email system to your main navigation
4. Consider setting up automated disaster alerts
5. Train users on the email templates and system
