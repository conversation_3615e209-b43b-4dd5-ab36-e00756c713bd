<?php
session_start();
require 'db_connect.php';
require 'email_config.php';
require 'simple_mailer.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

$message = '';
$messageType = '';

// Handle form submission
if ($_POST && isset($_POST['send_email'])) {
    $to_email = trim($_POST['to_email']);
    $subject = trim($_POST['subject']);
    $email_body = trim($_POST['email_body']);
    $from_name = trim($_POST['from_name']);
    
    // Validate inputs
    if (empty($to_email) || empty($subject) || empty($email_body)) {
        $message = 'Please fill in all required fields.';
        $messageType = 'error';
    } elseif (!filter_var($to_email, FILTER_VALIDATE_EMAIL)) {
        $message = 'Please enter a valid email address.';
        $messageType = 'error';
    } else {
        // Try to send email using PHP mail function first
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= "From: " . ($from_name ? $from_name : 'Disaster Watch System') . " <<EMAIL>>" . "\r\n";
        $headers .= "Reply-To: <EMAIL>" . "\r\n";
        
        $html_body = "
        <html>
        <head>
            <title>$subject</title>
        </head>
        <body>
            <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
                <h2 style='color: #1976d2;'>$subject</h2>
                <div style='background-color: #f5f5f5; padding: 20px; border-radius: 5px;'>
                    " . nl2br(htmlspecialchars($email_body)) . "
                </div>
                <hr style='margin: 20px 0;'>
                <p style='color: #666; font-size: 12px;'>
                    This email was sent from the Disaster Watch System.<br>
                    Sent by: " . htmlspecialchars($from_name ? $from_name : 'System Administrator') . "<br>
                    Date: " . date('Y-m-d H:i:s') . "
                </p>
            </div>
        </body>
        </html>";
        
        // Check if running on localhost (XAMPP) and use SMTP
        $is_localhost = (isset($_SERVER['HTTP_HOST']) &&
                        (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false ||
                         strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false));

        $email_sent = false;
        $error_message = '';

        if ($is_localhost || (defined('USE_SMTP') && USE_SMTP)) {
            // Use SMTP for localhost/XAMPP
            $result = sendEmailSimple($to_email, $subject, $email_body, $from_name);
            $email_sent = $result['success'];
            $error_message = $result['message'];
        } else {
            // Use PHP mail() for production servers
            if (mail($to_email, $subject, $html_body, $headers)) {
                $email_sent = true;
            } else {
                $error_message = 'Failed to send email using PHP mail()';
            }
        }

        if ($email_sent) {
            // Log the email in database
            $stmt = $conn->prepare("INSERT INTO email_logs (user_id, to_email, subject, body, sent_at, status) VALUES (?, ?, ?, ?, NOW(), 'sent')");
            $stmt->bind_param("isss", $_SESSION['user_id'], $to_email, $subject, $email_body);
            $stmt->execute();

            $message = 'Email sent successfully to ' . htmlspecialchars($to_email);
            $messageType = 'success';

            // Clear form data on success
            $_POST = array();
        } else {
            $message = $error_message ?: 'Failed to send email. Please check your configuration.';
            $messageType = 'error';

            // Log failed attempt
            $stmt = $conn->prepare("INSERT INTO email_logs (user_id, to_email, subject, body, sent_at, status, error_message) VALUES (?, ?, ?, ?, NOW(), 'failed', ?)");
            $stmt->bind_param("issss", $_SESSION['user_id'], $to_email, $subject, $email_body, $error_message);
            $stmt->execute();
        }
    }
}

// Get recent email logs for current user
$email_logs = [];
$stmt = $conn->prepare("SELECT * FROM email_logs WHERE user_id = ? ORDER BY sent_at DESC LIMIT 10");
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
while ($row = $result->fetch_assoc()) {
    $email_logs[] = $row;
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Send Email - Disaster Watch</title>
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <style>
        .email-form {
            margin-top: 20px;
        }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .email-logs {
            margin-top: 30px;
        }
        .log-item {
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #2196f3;
            background-color: #f5f5f5;
        }
        .log-item.failed {
            border-left-color: #f44336;
        }
        .log-item.sent {
            border-left-color: #4caf50;
        }
    </style>
</head>
<body>
    <?php include 'header.php'; ?>
    
    <nav class="blue darken-2 z-depth-0">
        <div class="nav-wrapper">
            <span style="margin-left:14.9px; font-size:17.6px;">
                <i class="material-icons left">email</i>Send Email
            </span>
            <a href="index.php" class="right btn transparent z-depth-0">
                <i class="material-icons left">arrow_back</i>Back to Dashboard
            </a>
        </div>
    </nav>

    <div class="container">
        <div class="row">
            <div class="col s12 m8 offset-m2">

                <?php
                // Check if running on XAMPP and show setup notice
                $is_localhost = (isset($_SERVER['HTTP_HOST']) &&
                                (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false ||
                                 strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false));

                if ($is_localhost && (!defined('SMTP_USERNAME') || SMTP_USERNAME === '<EMAIL>')): ?>
                    <div class="card-panel orange lighten-4">
                        <i class="material-icons left">info</i>
                        <strong>XAMPP Setup Required:</strong>
                        Email configuration needed for localhost.
                        <a href="xampp_email_setup.php" class="btn-small blue darken-2" style="margin-left: 10px;">
                            <i class="material-icons left">settings</i>Setup Now
                        </a>
                    </div>
                <?php endif; ?>

                <?php if ($message): ?>
                    <div class="message <?php echo $messageType; ?>">
                        <i class="material-icons left"><?php echo $messageType === 'success' ? 'check_circle' : 'error'; ?></i>
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-content">
                        <span class="card-title">
                            <i class="material-icons left">send</i>Compose Email
                        </span>
                        
                        <form method="POST" class="email-form">
                            <div class="row">
                                <div class="input-field col s12">
                                    <input type="email" id="to_email" name="to_email" required 
                                           value="<?php echo isset($_POST['to_email']) ? htmlspecialchars($_POST['to_email']) : ''; ?>">
                                    <label for="to_email">To Email Address *</label>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="input-field col s12">
                                    <input type="text" id="from_name" name="from_name" 
                                           value="<?php echo isset($_POST['from_name']) ? htmlspecialchars($_POST['from_name']) : ''; ?>">
                                    <label for="from_name">Your Name (optional)</label>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="input-field col s12">
                                    <input type="text" id="subject" name="subject" required 
                                           value="<?php echo isset($_POST['subject']) ? htmlspecialchars($_POST['subject']) : ''; ?>">
                                    <label for="subject">Subject *</label>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="input-field col s12">
                                    <textarea id="email_body" name="email_body" class="materialize-textarea" required 
                                              rows="10"><?php echo isset($_POST['email_body']) ? htmlspecialchars($_POST['email_body']) : ''; ?></textarea>
                                    <label for="email_body">Message *</label>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col s12">
                                    <button type="submit" name="send_email" class="btn blue darken-2 waves-effect waves-light">
                                        <i class="material-icons left">send</i>Send Email
                                    </button>
                                    <button type="reset" class="btn grey waves-effect waves-light">
                                        <i class="material-icons left">clear</i>Clear Form
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Email Templates Section -->
                <div class="card">
                    <div class="card-content">
                        <span class="card-title">
                            <i class="material-icons left">description</i>Quick Templates
                        </span>
                        <div class="row">
                            <div class="col s12 m6">
                                <a href="#" class="btn-flat blue-text template-btn" 
                                   data-subject="Disaster Alert - Immediate Action Required"
                                   data-body="Dear Recipient,&#10;&#10;This is an urgent notification regarding a disaster situation in your area. Please take immediate precautionary measures and follow the guidelines provided by local authorities.&#10;&#10;Stay safe and stay informed.&#10;&#10;Best regards,&#10;Disaster Watch Team">
                                    <i class="material-icons left">warning</i>Disaster Alert Template
                                </a>
                            </div>
                            <div class="col s12 m6">
                                <a href="#" class="btn-flat blue-text template-btn"
                                   data-subject="Evacuation Notice"
                                   data-body="Dear Resident,&#10;&#10;Due to current conditions, an evacuation order has been issued for your area. Please proceed to the designated evacuation center immediately.&#10;&#10;Evacuation Center: [Location]&#10;Contact: [Phone Number]&#10;&#10;Please bring essential items and follow evacuation procedures.&#10;&#10;Emergency Response Team">
                                    <i class="material-icons left">directions_run</i>Evacuation Notice
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Email Logs -->
                <?php if (!empty($email_logs)): ?>
                <div class="card email-logs">
                    <div class="card-content">
                        <span class="card-title">
                            <i class="material-icons left">history</i>Recent Emails
                        </span>
                        <?php foreach ($email_logs as $log): ?>
                            <div class="log-item <?php echo $log['status']; ?>">
                                <div class="row valign-wrapper" style="margin-bottom: 0;">
                                    <div class="col s8">
                                        <strong><?php echo htmlspecialchars($log['subject']); ?></strong><br>
                                        <small>To: <?php echo htmlspecialchars($log['to_email']); ?></small>
                                    </div>
                                    <div class="col s4 right-align">
                                        <span class="chip <?php echo $log['status'] === 'sent' ? 'green white-text' : 'red white-text'; ?>">
                                            <?php echo ucfirst($log['status']); ?>
                                        </span><br>
                                        <small><?php echo date('M j, Y g:i A', strtotime($log['sent_at'])); ?></small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Materialize components
            M.AutoInit();
            
            // Template button functionality
            document.querySelectorAll('.template-btn').forEach(function(btn) {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.getElementById('subject').value = this.dataset.subject;
                    document.getElementById('email_body').value = this.dataset.body.replace(/&#10;/g, '\n');
                    
                    // Update labels
                    M.updateTextFields();
                    M.textareaAutoResize(document.getElementById('email_body'));
                });
            });
        });
    </script>
</body>
</html>
