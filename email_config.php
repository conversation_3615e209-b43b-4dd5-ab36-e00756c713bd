<?php
// Email Configuration Settings
// For Gmail SMTP, you'll need to:
// 1. Enable 2-factor authentication on your Gmail account
// 2. Generate an App Password for this application
// 3. Use the App Password instead of your regular password

// SMTP Configuration for Gmail
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_SECURE', 'tls'); // or 'ssl' for port 465
define('SMTP_AUTH', true);

// Gmail credentials (replace with your actual credentials)
define('SMTP_USERNAME', '<EMAIL>'); // Your Gmail address
define('SMTP_PASSWORD', 'your-app-password');    // Your Gmail App Password (not regular password)
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'Disaster Watch System');

// Alternative: Use environment variables for security (recommended for production)
// define('SMTP_USERNAME', $_ENV['GMAIL_USERNAME'] ?? '<EMAIL>');
// define('SMTP_PASSWORD', $_ENV['GMAIL_APP_PASSWORD'] ?? 'your-app-password');

// Email settings
define('EMAIL_ENABLED', true); // Set to false to disable email sending
define('USE_SMTP', false); // Set to true to use SMTP, false to use PHP mail()

// Default email templates
$email_templates = [
    'disaster_alert' => [
        'subject' => 'Disaster Alert - Immediate Action Required',
        'body' => "Dear Recipient,\n\nThis is an urgent notification regarding a disaster situation in your area. Please take immediate precautionary measures and follow the guidelines provided by local authorities.\n\nStay safe and stay informed.\n\nBest regards,\nDisaster Watch Team"
    ],
    'evacuation_notice' => [
        'subject' => 'Evacuation Notice',
        'body' => "Dear Resident,\n\nDue to current conditions, an evacuation order has been issued for your area. Please proceed to the designated evacuation center immediately.\n\nEvacuation Center: [Location]\nContact: [Phone Number]\n\nPlease bring essential items and follow evacuation procedures.\n\nEmergency Response Team"
    ],
    'safety_update' => [
        'subject' => 'Safety Update - Current Situation',
        'body' => "Dear Community Member,\n\nWe are providing you with an update on the current situation in your area.\n\n[Update Details]\n\nPlease continue to follow safety guidelines and stay informed through official channels.\n\nStay safe,\nDisaster Watch Team"
    ],
    'all_clear' => [
        'subject' => 'All Clear - Normal Operations Resumed',
        'body' => "Dear Resident,\n\nWe are pleased to inform you that the emergency situation has been resolved and normal operations have resumed.\n\nThank you for your cooperation during this time.\n\nBest regards,\nDisaster Watch Team"
    ]
];

// Function to get email template
function getEmailTemplate($template_name) {
    global $email_templates;
    return isset($email_templates[$template_name]) ? $email_templates[$template_name] : null;
}

// Function to validate email configuration
function validateEmailConfig() {
    if (!EMAIL_ENABLED) {
        return ['status' => false, 'message' => 'Email sending is disabled'];
    }
    
    if (USE_SMTP) {
        if (empty(SMTP_USERNAME) || empty(SMTP_PASSWORD)) {
            return ['status' => false, 'message' => 'SMTP credentials not configured'];
        }
        if (SMTP_USERNAME === '<EMAIL>' || SMTP_PASSWORD === 'your-app-password') {
            return ['status' => false, 'message' => 'Please update SMTP credentials in email_config.php'];
        }
    }
    
    return ['status' => true, 'message' => 'Email configuration is valid'];
}
?>
