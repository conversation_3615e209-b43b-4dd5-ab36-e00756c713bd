<?php
require 'db_connect.php';
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

$result = $conn->query("SELECT r.*, u.username FROM reports r JOIN users u ON r.user_id=u.id ORDER BY r.time DESC");
$barangay = $_SESSION['barangay'];
?>
<?php
// header.php
 
 
// Check login status
$isLoggedIn = isset($_SESSION['user_id']);
$isAdmin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
?>
<?php include 'header.php'; ?>

<style>
  .export-card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }
  .field-checkbox {
    margin: 8px 0;
  }
  .export-preview {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    background: #f9f9f9;
  }
  .progress-container {
    display: none;
    margin: 20px 0;
  }
</style>

<div class="container" style="margin-top: 20px;">
  <div class="row">
    <div class="col s12">
      <h4 class="blue-text text-darken-2">
        <i class="material-icons left">file_download</i>
        Export Data to Excel
      </h4>
    </div>
  </div>

  <div class="row">
    <!-- Filters Section -->
    <div class="col s12 m4">
      <div class="card export-card">
        <div class="card-content">
          <span class="card-title blue-text">
            <i class="material-icons left">filter_list</i>
            Filters
          </span>
          
          <form id="exportFiltersForm">
            <div class="input-field">
              <input id="date_from" name="date_from" type="date" class="validate">
              <label for="date_from" class="active">Date From</label>
            </div>
            
            <div class="input-field">
              <input id="date_to" name="date_to" type="date" class="validate">
              <label for="date_to" class="active">Date To</label>
            </div>
            
            <div class="input-field">
              <select id="event_type" name="event_type">
                <option value="">All Events</option>
                <option value="GEOLOGIC">Geologic Events</option>
                <option value="WEATHER">Weather Events</option>
                <option value="MANMADE">Man-made Events</option>
              </select>
              <label>Type of Event</label>
            </div>
            
            <div class="input-field">
              <select id="barangay_filter" name="barangay">
                <option value="">All Barangays</option>
                <option value="BACLARAN">Baclaran</option>
                <option value="BF HOMES">BF Homes</option>
                <option value="DON BOSCO">Don Bosco</option>
                <option value="DON GALO">Don Galo</option>
                <option value="LA HUERTA">La Huerta</option>
                <option value="MARCELO GREEN">Marcelo Green</option>
                <option value="MERVILLE">Merville</option>
                <option value="MOONWALK">Moonwalk</option>
                <option value="SAN ANTONIO">San Antonio</option>
                <option value="SAN DIONISIO">San Dionisio</option>
                <option value="SAN ISIDRO">San Isidro</option>
                <option value="SAN MARTIN DE PORRES">San Martin de Porres</option>
                <option value="SANTO NIÑO">Santo Niño</option>
                <option value="SUN VALLEY">Sun Valley</option>
                <option value="TAMBO">Tambo</option>
                <option value="VITALEZ">Vitalez</option>
              </select>
              <label>Barangay</label>
            </div>
            
            <button class="btn blue waves-effect waves-light" type="button" id="previewBtn">
              <i class="material-icons left">preview</i>Preview Data
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- Field Selection Section -->
    <div class="col s12 m4">
      <div class="card export-card">
        <div class="card-content">
          <span class="card-title green-text">
            <i class="material-icons left">check_box</i>
            Select Fields
          </span>
          
          <div style="margin-top: 20px;">
            <p>
              <label>
                <input type="checkbox" id="selectAll" checked />
                <span><strong>Select All</strong></span>
              </label>
            </p>
            
            <div id="fieldsList">
              <!-- Basic Information -->
              <h6 class="blue-text">Basic Information</h6>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="id" checked />
                  <span>Record ID</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="activity_date" checked />
                  <span>Activity Date</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="activity_time" checked />
                  <span>Activity Time</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="barangay" checked />
                  <span>Barangay</span>
                </label>
              </p>
              
              <!-- Event Types -->
              <h6 class="blue-text">Event Types</h6>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="GEOLOGIC" checked />
                  <span>Geologic Events</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="WEATHER" checked />
                  <span>Weather Events</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="MANMADE" checked />
                  <span>Man-made Events</span>
                </label>
              </p>
              
              <!-- Affected Population -->
              <h6 class="blue-text">Affected Population</h6>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="affected_areas" checked />
                  <span>Affected Areas</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="individuals_affected" checked />
                  <span>Individuals Affected</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="families_affected" checked />
                  <span>Families Affected</span>
                </label>
              </p>
              
              <!-- Evacuation Data -->
              <h6 class="blue-text">Evacuation Data</h6>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="evacuation_site" checked />
                  <span>Evacuation Site</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="evac_individuals" checked />
                  <span>Evacuated Individuals</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="evac_families" checked />
                  <span>Evacuated Families</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="evac_male" checked />
                  <span>Male Evacuees</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="evac_female" checked />
                  <span>Female Evacuees</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="evac_pregnant" checked />
                  <span>Pregnant Evacuees</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="evac_pwd" checked />
                  <span>PWD Evacuees</span>
                </label>
              </p>
              
              <!-- Age Groups -->
              <h6 class="blue-text">Age Groups</h6>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="evac_under_5" checked />
                  <span>Under 5 years</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="evac_5_to_17" checked />
                  <span>5-17 years</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="evac_18_to_59" checked />
                  <span>18-59 years</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="evac_60_above" checked />
                  <span>60+ years</span>
                </label>
              </p>
              
              <!-- Health Data -->
              <h6 class="blue-text">Health Data</h6>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="no_of_casualties" />
                  <span>Number of Casualties</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="no_of_treated_on_site" />
                  <span>Treated on Site</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="no_of_brought_to_hospital" />
                  <span>Brought to Hospital</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="no_of_deaths" />
                  <span>Number of Deaths</span>
                </label>
              </p>
              <p class="field-checkbox">
                <label>
                  <input type="checkbox" name="fields" value="no_of_missing" />
                  <span>Number Missing</span>
                </label>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Preview and Export Section -->
    <div class="col s12 m4">
      <div class="card export-card">
        <div class="card-content">
          <span class="card-title orange-text">
            <i class="material-icons left">visibility</i>
            Preview & Export
          </span>
          
          <div id="recordCount" class="chip blue white-text" style="margin: 10px 0;">
            <i class="material-icons left">info</i>
            No data loaded
          </div>
          
          <div class="export-preview" id="dataPreview">
            <p class="grey-text center-align">Click "Preview Data" to see filtered results</p>
          </div>
          
          <div class="progress-container" id="progressContainer">
            <div class="progress">
              <div class="determinate" id="exportProgress" style="width: 0%"></div>
            </div>
            <p class="center-align" id="progressText">Preparing export...</p>
          </div>
          
          <div style="margin-top: 20px;">
            <button class="btn green waves-effect waves-light btn-large" type="button" id="exportBtn" disabled>
              <i class="material-icons left">file_download</i>Export to Excel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
$(document).ready(function() {
  // Initialize Materialize components
  M.FormSelect.init(document.querySelectorAll('select'));
  
  let currentData = [];
  
  // Select All functionality
  $('#selectAll').on('change', function() {
    const isChecked = $(this).is(':checked');
    $('input[name="fields"]').prop('checked', isChecked);
  });
  
  // Individual checkbox change
  $('input[name="fields"]').on('change', function() {
    const totalFields = $('input[name="fields"]').length;
    const checkedFields = $('input[name="fields"]:checked').length;
    $('#selectAll').prop('checked', totalFields === checkedFields);
  });
  
  // Preview data
  $('#previewBtn').on('click', function() {
    previewData();
  });
  
  // Export data
  $('#exportBtn').on('click', function() {
    exportToExcel();
  });
  
  function previewData() {
    const formData = new FormData(document.getElementById('exportFiltersForm'));
    
    $('#dataPreview').html(`
      <div class="center-align">
        <div class="preloader-wrapper small active">
          <div class="spinner-layer spinner-blue-only">
            <div class="circle-clipper left"><div class="circle"></div></div>
            <div class="gap-patch"><div class="circle"></div></div>
            <div class="circle-clipper right"><div class="circle"></div></div>
          </div>
        </div>
        <p>Loading data...</p>
      </div>
    `);
    
    fetch('fetch_export_data.php', {
      method: 'POST',
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        currentData = data.records;
        displayPreview(data.records);
        $('#recordCount').html(`<i class="material-icons left">info</i>${data.count} records found`);
        $('#exportBtn').prop('disabled', data.count === 0);
      } else {
        $('#dataPreview').html(`<p class="red-text center-align">${data.message}</p>`);
        $('#recordCount').html(`<i class="material-icons left">error</i>Error loading data`);
        $('#exportBtn').prop('disabled', true);
      }
    })
    .catch(error => {
      console.error('Preview error:', error);
      $('#dataPreview').html(`<p class="red-text center-align">Error loading data</p>`);
      $('#exportBtn').prop('disabled', true);
    });
  }
  
  function displayPreview(records) {
    if (records.length === 0) {
      $('#dataPreview').html('<p class="center-align grey-text">No records found with current filters</p>');
      return;
    }
    
    const selectedFields = getSelectedFields();
    let html = '<table class="striped responsive-table"><thead><tr>';
    
    // Headers
    selectedFields.forEach(field => {
      html += `<th>${getFieldLabel(field)}</th>`;
    });
    html += '</tr></thead><tbody>';
    
    // Show first 5 records as preview
    const previewRecords = records.slice(0, 5);
    previewRecords.forEach(record => {
      html += '<tr>';
      selectedFields.forEach(field => {
        html += `<td>${record[field] || '-'}</td>`;
      });
      html += '</tr>';
    });
    
    html += '</tbody></table>';
    
    if (records.length > 5) {
      html += `<p class="center-align grey-text"><em>Showing first 5 of ${records.length} records</em></p>`;
    }
    
    $('#dataPreview').html(html);
  }
  
  function getSelectedFields() {
    const fields = [];
    $('input[name="fields"]:checked').each(function() {
      fields.push($(this).val());
    });
    return fields;
  }
  
  function getFieldLabel(field) {
    const labels = {
      'id': 'Record ID',
      'activity_date': 'Activity Date',
      'activity_time': 'Activity Time',
      'barangay': 'Barangay',
      'GEOLOGIC': 'Geologic Events',
      'WEATHER': 'Weather Events',
      'MANMADE': 'Man-made Events',
      'affected_areas': 'Affected Areas',
      'individuals_affected': 'Individuals Affected',
      'families_affected': 'Families Affected',
      'evacuation_site': 'Evacuation Site',
      'evac_individuals': 'Evacuated Individuals',
      'evac_families': 'Evacuated Families',
      'evac_male': 'Male Evacuees',
      'evac_female': 'Female Evacuees',
      'evac_pregnant': 'Pregnant Evacuees',
      'evac_pwd': 'PWD Evacuees',
      'evac_under_5': 'Under 5 years',
      'evac_5_to_17': '5-17 years',
      'evac_18_to_59': '18-59 years',
      'evac_60_above': '60+ years',
      'no_of_casualties': 'Casualties',
      'no_of_treated_on_site': 'Treated on Site',
      'no_of_brought_to_hospital': 'Brought to Hospital',
      'no_of_deaths': 'Deaths',
      'no_of_missing': 'Missing'
    };
    return labels[field] || field;
  }
  
  function exportToExcel() {
    const selectedFields = getSelectedFields();
    
    if (selectedFields.length === 0) {
      M.toast({html: 'Please select at least one field to export', classes: 'orange'});
      return;
    }
    
    if (currentData.length === 0) {
      M.toast({html: 'No data to export', classes: 'orange'});
      return;
    }
    
    // Show progress
    $('#progressContainer').show();
    $('#exportBtn').prop('disabled', true);
    
    // Simulate progress
    let progress = 0;
    const progressInterval = setInterval(() => {
      progress += 10;
      $('#exportProgress').css('width', progress + '%');
      $('#progressText').text(`Preparing export... ${progress}%`);
      
      if (progress >= 90) {
        clearInterval(progressInterval);
      }
    }, 100);
    
    // Prepare export data
    const exportData = {
      records: currentData,
      fields: selectedFields,
      filters: Object.fromEntries(new FormData(document.getElementById('exportFiltersForm')))
    };
    
    fetch('export_to_excel.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(exportData)
    })
    .then(response => {
      if (!response.ok) {
        throw new Error('Export failed');
      }
      return response.blob();
    })
    .then(blob => {
      // Complete progress
      clearInterval(progressInterval);
      $('#exportProgress').css('width', '100%');
      $('#progressText').text('Download starting...');
      
      // Download file
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `evacuation_reports_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      
      // Hide progress
      setTimeout(() => {
        $('#progressContainer').hide();
        $('#exportBtn').prop('disabled', false);
        $('#exportProgress').css('width', '0%');
        M.toast({html: 'Export completed successfully!', classes: 'green'});
      }, 1000);
    })
    .catch(error => {
      console.error('Export error:', error);
      clearInterval(progressInterval);
      $('#progressContainer').hide();
      $('#exportBtn').prop('disabled', false);
      M.toast({html: 'Export failed. Please try again.', classes: 'red'});
    });
  }
});
</script>

<?php include 'footer.php'; ?>