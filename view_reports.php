<?php
require 'db_connect.php';
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

$result = $conn->query("SELECT r.*, u.username FROM reports r JOIN users u ON r.user_id=u.id ORDER BY r.time DESC");
$barangay = $_SESSION['barangay'];
?>
<?php include 'header.php'; ?>
 <script src="https://code.highcharts.com/highcharts.js"></script>
<script src="https://code.highcharts.com/modules/exporting.js"></script>
<script src="https://code.highcharts.com/modules/export-data.js"></script>
<script src="https://code.highcharts.com/modules/accessibility.js"></script>
 
<nav class="  blue darken-2 z-depth-0">
  <span style="margin-left:12.9px; font-size:17.6px;">Data Analysis: EVENT MONITORING & RESPONSE ACTIVITY REPORT FOR HEALTH FACILITIES</span>
</nav>

<figure class="highcharts-figure" >
 
<div class="row  ">
     
    
      <div class="col s3 offset-s9 ">  <input type="date" name="" class="" id=""> </div>
    </div>
   
    </div>
   


    
    <div id="container"></div>
 
</figure>
<figure class="highcharts-figure">
 
    <div id="container2"></div>
     
</figure>
<figure class="highcharts-figure">
 <h5>Magnitude of Event</h5>
    <div id="container3"></div>
    <p class="highcharts-description">
        
    </p>
</figure>


<figure class="highcharts-figure">
 
    <div id="container4"></div>
    <p class="highcharts-description">
        
    </p>
</figure>
<figure class="highcharts-figure">
 <h5>No. of Population in Displacement</h5>
    <div id="container3"></div>
    <p class="highcharts-description">
        
    </p>
</figure>


 

<?php
include 'db_connect.php'; 

$permonthJson = [];
$counts = [];

$sql = "SELECT  (activity_date) AS permonth, COUNT(*) AS registration_count 
        FROM evacuation_reports 
        GROUP BY permonth 
        ORDER BY MIN(activity_date)"; // Ensures months are in correct order

$result = $conn->query($sql); // Use query() directly

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $permonthJson[] = date('F d,Y',strtotime($row['permonth']));
        $counts[] = (int)$row['registration_count'];
    }
}

$permonthJson = json_encode($permonthJson);
$countsJson = json_encode($counts);

$conn->close();
?>

<script>
    const permonthJson = <?php echo $permonthJson ; ?>;
    const countsJson = <?php echo $countsJson; ?>;

    Highcharts.chart('container', {
        
        chart: {
            type: 'column'
        },
        title: {
            text: 'Descriptive Analysis Per Date of Activity (N=90)',
            fontweight: 'bolder'
        },
        subtitle: {
            text: 'Parañaque City'
        },
        
        xAxis: {
  gridLineWidth: 0,
            categories: permonthJson,
            crosshair: true
        },
        yAxis: {
              lineWidth: 1.2,
           gridLineWidth: 0,
            min: 0,
            title: {
                text: ''
            }
        },
        tooltip: {
            valueSuffix: ' reports'
        },
        plotOptions: {
            
            column: {
                pointPadding: 0.2,
                borderWidth: 0
            }
        },
        series: [{
         pointWidth: '690',
            name: 'Per Day ',
            data: countsJson,
            color: '#00acc1'
        }],
        
    });
</script>
  






<?php
include 'db_connect.php'; 
$barangayJson = [];
$sql = "SELECT barangay AS br, COUNT(*) AS brgy_count 
        FROM evacuation_reports 
        GROUP BY barangay ORDER BY br"; // Sort by barangay name

$result = $conn->query($sql); // No need for prepare() if no parameters

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $barangayJson[] = $row['br'];
        $countsbrgyJson[] = (int)$row['brgy_count'];
    }
}

$barangayJson = json_encode($barangayJson);
$countsbrgyJson = json_encode($countsbrgyJson);

$conn->close();
?>
<script>
    const barangayJson = <?php echo $barangayJson; ?>;
    const countsbrgyJson = <?php echo $countsbrgyJson; ?>;

    Highcharts.chart('container2', {
        chart: {
            type: 'column'
        },
        title: {
            text: 'Descriptive Analysis Per Barangay (N=90)'
        },
        subtitle: {
            text: 'Parañaque City'
        },
        xAxis: {
             gridLineWidth: 0,
            categories: barangayJson,
            crosshair: false,
            accessibility: {
                description: 'Barangays'
            }
        },
        yAxis: {
              lineWidth: 1.2,
             gridLineWidth: 0,
            min: 0,
            title: {
                text: ''
            }
        },
        tooltip: {
            valueSuffix: ' reports'
        },
        plotOptions: {
            column: {
                pointPadding: 0.2,
                borderWidth: 0
            }
        },
        series: [{
         pointWidth: '690',
            name: 'Barangay',
            data: countsbrgyJson,
            color: '#1e88e5'
        }]
    });
</script>
 







<?php
include 'db_connect.php'; 

$affected_areas = [];
$counts = [];

$sql = "SELECT affected_areas AS br, COUNT(*) AS affected_areas_count 
        FROM evacuation_reports 
        GROUP BY affected_areas 
        ORDER BY br";

$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $affected_areas[] = $row['br'];
        $counts[] = (int)$row['affected_areas_count'];
    }
}

$affected_areas_json = json_encode($affected_areas);
$counts_json = json_encode($counts);

$conn->close();
?>

<script>
    const affectedAreas = <?php echo $affected_areas_json; ?>;
    const countsAffected = <?php echo $counts_json; ?>;

    Highcharts.chart('container3', {
        chart: {
            type: 'column'
        },
        title: {
            text: 'Descriptive Analysis Per Affected Area (N=90)'
        },
        subtitle: {
            text: 'Parañaque City'
        },
        xAxis: {
             gridLineWidth: 0,
            categories: affectedAreas,
            crosshair: true,
            accessibility: {
                description: 'Affected Areas'
            }
        },
        yAxis: {
            
             gridLineWidth: 0,
           min: 0,
            title: {
                text: ' '
            }
        },
        tooltip: {
            valueSuffix: ' reports'
        },
        plotOptions: {
            column: {
                pointPadding: 0.2,
                borderWidth: 0
            }
        },
        series: [{
              pointWidth: '690',
            name: 'Reports',
            data: countsAffected,
            color: '#66bb6a'
        }]
    });
</script>
  







<?php
include 'db_connect.php'; 

$individuals_affected = [];
$counts = [];

$sql = "SELECT individuals_affected AS br, COUNT(*) AS individuals_affected_count 
        FROM evacuation_reports 
        GROUP BY individuals_affected 
        ORDER BY individuals_affected ASC"; // Use numeric order if it's numeric

$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $individuals_affected[] = $row['br']; // e.g. 5, 10, 15
        $counts[] = (int)$row['individuals_affected_count'];
    }
}

$individuals_affected_json = json_encode($individuals_affected);
$counts_json = json_encode($counts);

$conn->close();
?>

<script>
   individuals_affected_json = <?php echo $individuals_affected_json; ?>;
    const countsIndividuals = <?php echo $counts_json; ?>;
 Highcharts.chart('container4', {
    chart: {
        type: 'column'
    },
    title: {
        text: 'Descriptive Analysis Per Number of Individuals Affected (N=90)'
    },
    subtitle: {
        text:
            'Parañaque City'  
    },
    xAxis: {
         gridLineWidth: 0,
        categories: individuals_affected_json,
        crosshair: true,
   
        accessibility: {
            description: ' '
        }
    },
    yAxis: {
        lineWidth: 1.2,
         gridLineWidth: 0,
        min: 0,
        title: {
            text: ''
        }
    },
    tooltip: {
        valueSuffix: ' (1000 MT)'
    },
    plotOptions: {
        line: {
 
        },
        column: {
            pointPadding: 0.2,
            borderWidth: 0
        }
    },
    
    series: [
        {
                       pointWidth: '456',
            name: 'n',
            data:countsIndividuals,
            color: '#ab47bc'
        } 
    ],
     responsive: {
            rules: [{
                condition: {
                    maxWidth: 500
                },
                chartOptions: {
                    legend: {
                        align: 'center',
                        verticalAlign: 'center',
                        layout: 'horizontal'
                    },
                    yAxis: {
                        title: {
                            text: ''
                        }
                    },
                    subtitle: {
                        text: ''
                    }
                }
            }]
        }
});

</script>   

<?php include 'footer.php'; ?>
