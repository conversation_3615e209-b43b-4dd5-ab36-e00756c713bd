<?php
require 'db_connect.php';
$message = "";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $fullname = trim($_POST['fullname']);
    $username = $conn->real_escape_string(trim($_POST['username']));
    $password = trim($_POST['password']);
    $barangay = trim($_POST['barangay']);
    $mobile_number = trim($_POST['mobile_number']);
    $email = trim($_POST['email']);
    $designation_office = trim($_POST['designation_office']);

    // Hash password securely
    $hash = password_hash($password, PASSWORD_DEFAULT);

    // Prepare and bind the insert statement
    $stmt = $conn->prepare("INSERT INTO users (fullname, username, password, barangay, mobile_number,email, designation_office, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
    if ($stmt) {
        $status = 'pending';
        $stmt->bind_param("ssssssss", $fullname, $username, $hash, $barangay, $mobile_number,$email, $designation_office, $status);

        if ($stmt->execute()) {
            $message = "<span class='center'>Registration successful. <a href='login.php'>Login here</a>.</span>";
        } else {
            $message = "<span class='center red-text'>Error during registration: " . $stmt->error."</span>";
        }

        $stmt->close();
    } else {
        $message = "Failed to prepare the SQL statement.";
    }
}

?>


<?php include 'header.php'; ?>
 <div class=" ">
   
        <div class="row ">
 
        <div class="col  s12 m7 push-m2">
       
        <div class="card z-depth-0" style="margin-top:14px; border:1px solid #ddd;">
            
                    <div class="card-content">
<h5 class="blue-text text-darken-4" style="margin-left:9px;">Create Account</h5>
<form action="register.php" method="post">
  <div class="input-field col s12 m6">
    <input id="fullname" name="fullname" type="text" required>
    <label for="fullname">Fullname</label>
  </div>
  <div class="input-field col s12 m6">
    <input id="username" name="username" type="text" required>
    <label for="username">Username</label>
  </div>
  <div class="input-field col s12 m12">
    <input id="password" name="password" type="password" required>
    <label for="password">Password</label>
  </div>
  <div class="input-field input-field col s12 m6">
    <select name="barangay" required>
      <option value="" disabled selected> </option>
<option value="BACLARAN">Baclaran</option>
<option value="DON GALO">Don Galo</option>
<option value="LA HUERTA">La Huerta</option>
<option value="SAN DIONISIO">San Dionisio</option>
<option value="SAN ISIDRO">San Isidro</option>
<option value="SAN ANTONIO">San Antonio</option>
<option value="MOONWALK">Moonwalk</option>
<option value="MERVILLE">Merville</option>
<option value="SUN VALLEY">Sun Valley</option>
<option value="MARCELO GREEN">Marcelo Green</option>
<option value="BF HOMES">BF Homes</option>
<option value="DON BOSCO">Don Bosco</option>
<option value="TAMBO">Tambo</option>
<option value="STO. NIÑO">Sto. Niño</option>
<option value="VITALEZ">Vitalez</option>
<option value="SAN MARTIN DE PORRES">San Martin De Porres</option>

      
    </select>
    <label>Barangay</label>
  </div>
    <div class="input-field col s12 m6">
    <input id="mobile_number" name="mobile_number" type="text" required>
    <label for="mobile_number">Mobile Number</label>
  </div>
    <div class="input-field col s12 m12">
    <input id="email" name="email" type="email" required>
    <label for="email">Email</label>
  </div>
     <div class="input-field col s12 m12">
    <input id="designation_office" name="designation_office" type="text" required>
    <label for="designation_office">Designation Office</label>
  </div>
  <button class="btn blue waves-effect" type="submit">Register</button>
</form>

  </div>
    </div>
      </div>
        </div>
          </div>
<p><?php echo $message; ?></p>
<?php include 'footer.php'; ?>
