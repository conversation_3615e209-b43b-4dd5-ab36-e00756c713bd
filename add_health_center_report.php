<?php
require 'db_connect.php';
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

$result = $conn->query("SELECT r.*, u.username FROM reports r JOIN users u ON r.user_id=u.id ORDER BY r.time DESC");
$barangay = $_SESSION['barangay'];
?>
<?php include 'header.php'; ?>
<style>

  ::placeholder {
  color:#78909c;
 
}
</style>
 
  

<form id="evacuationForm" class="     row  " style="padding:1px;margin-top:11px; margin:00px; background:#e7f0f6;">
   <br>
   <div class="  col white s10 offset-s1" style="margin-top:10px;">
      <h5 style="font-weight:500; font-size:17.4px;padding-left:12px;padding-top:0px;padding-bottom:10px;" class="">Data Entry:EVENT MONITORING & RESPONSE ACTIVITY REPORT FOR HEALTH FACILITIES </h5>
     <div class="    col s11"style="margin-left:12px;padding-top:2px;padding-bottom:2px;background:#e7f0f6;color:#00658e">
    <small > (This form shall be filled-out and submitted by the Primary Care Facilities to the CHO-HEMS within 24 hours upon occurrence of a major health emergency or disaster and/or the establishment of evacuation centers.)</i></small> 
   </div>
       <div class="  col s12">
<h5 style="font-size:14.4px;font-weight:500; padding-top:15px; padding-bottom:15px; padding-left:10px;background:#00658e;" class="  white-text">A. Event Information</h5>
<!-- Add fields below -->
  </div>
    <div class="input-field col s12 z-depth-1" style="margin:10px;padding:11px;">
      <h5 class="black white-text" style="font-size:13.6px; padding-top:10px; padding-bottom:10px; padding-left:10px;">I.Type of Event</h5>
  <p class=" " style="padding:7px;">
    <span class="black-text"  style="">GEOLOGIC</span> <br>
      <label >
        <input name="GEOLOGIC" value="Earthquake" type="radio" class="with-gap"   />
        <span>Earthquake  </span>
      </label>
      <label>
        <input name="GEOLOGIC" value="Ashfall" type="radio" class="with-gap"   />
        <span>Ashfall</span>
      </label>
      <label>
        <input name="GEOLOGIC" value="Tsunami" type="radio" class="with-gap"   />
        <span>Tsunami  </span>
      </label>
      <label>
        <input name="GEOLOGIC" value="Liquefaction" type="radio" class="with-gap"   />
        <span>Liquefaction   </span>
      </label>
    </p>
 <p class=" " style="padding:7px;">
   <span class="black-text"  style="">WEATHER</span> <br>
      <label>
        <input name="WEATHER" value="Typhoon" type="radio" class="with-gap"   />
        <span>Typhoon  </span>
      </label>
      <label>
        <input name="WEATHER" value="Monsoon Rain" type="radio" class="with-gap"   />
        <span>Monsoon Rain</span>
      </label>
      <label>
        <input name="WEATHER" value="Storm Surge" type="radio" class="with-gap"   />
        <span>Storm Surge  </span>
      </label>
      <label>
        <input name="WEATHER" type="radio" class="with-gap"   />
        <span>Flashflood   </span>
      </label>
    </p>
 <p class=" " style="padding:7px;">
   <span class="black-text" style="">MAN-MADE</span> <br>
      <label>
        <input name="MANMADE" value="Fire" type="radio" class="with-gap"   />
        <span>Fire  </span>
      </label>
      <label>
        <input name="MANMADE" value="Explosion" type="radio" class="with-gap"   />
        <span>Explosion</span>
      </label>
      <label>
        <input name="MANMADE" value="Poisoning" type="radio" class="with-gap"   />
        <span>Poisoning</span>
      </label>
      <label>
        <input name="MANMADE" value="Terrorism" type="radio" class="with-gap"   />
        <span>Terrorism</span>
      </label>
      <label>
        <input name="MANMADE" value="Mass Action" type="radio" class="with-gap"   />
        <span>Mass Action</span>
      </label>
      <label>
        <input name="MANMADE" value="Accident" type="radio" class="with-gap"   />
        <span>Accident</span>
      </label>
      <label>
        <input name="MANMADE" value="Chemical Leak" type="radio" class="with-gap"   />
        <span>Chemical Leak</span>
      </label>
      <label>
        <input name="MANMADE" value="Others" type="radio" class="with-gap"   />
        <span>Others</span>
      </label>
    </p>
  
    
  </div>
    <div class="input-field col m3 s12">
    <input type="date" name="activity_date"   >
    <label class="active">Date of Activity</label>
  </div>
  <div class="input-field col m3 s12">
    <input type="time" name="activity_time"  >
    <label class="active">Time of Activity</label>
  </div>
  <div class="input-field col  m3 s12">
    <select name="barangay" class="blue accent-3 white-text"  readonly>
      <option class="blue accent-3 white-text" style="font-weight:500;" value="<?php echo $_SESSION['barangay']; ?>"   selected><?php echo $_SESSION['barangay']; ?></option>
      
    </select>
    <label>Barangay</label>
    
  </div>
  <div class="input-field col s12">
    <input type="text" name="additional_info"  >
    <label class="active">Any additional information about the event (not previously reported):</label>
  </div>
  </div>
 
   <div class="  col white s10 offset-s1" style="margin-top:10px;">
    <div class="  col s12  ">
<h5 style="font-size:14.4px;font-weight:500; padding-top:15px; padding-bottom:15px; padding-left:10px;background:#00658e;" class="  white-text">B. Magnitude of Event</h5>
<!-- Add fields below -->
  </div>

 <div class="input-field col m6 s12 ">
   <input type="text" name="affected_areas"  >
    <label class="active">Affected Areas</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="individuals_affected"  >
    <label class="active">No. of Individuals Affected</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="families_affected"  >
    <label class="active">No. of Families Affected</label>
  </div>
 <div class="input-field col m6 s12">
   <input list="evacuation_sites" type="text" name="evacuation_site"  >

   <datalist id="evacuation_sites">
<option value="La Huerta Elementary School">La Huerta Elementary School</option>
<option value="Don Bosco Gym">Don Bosco Gym</option>
<option value="San Antonio Barangay Hall">San Antonio Barangay Hall</option>
<option value="Area 1 Disaster Building, Sitio Libjo">Area 1 Disaster Building, Sitio Libjo</option>
<option value="Beldevere Covered Court">Beldevere Covered Court</option>
<option value="San Agustin Covered Court">San Agustin Covered Court</option>
<option value="Don Galo Gym">Don Galo Gym</option>
<option value="SANES Silverio">SANES Silverio</option>
<option value="Dr. FC Santos Compound">Dr. FC Santos Compound</option>
<option value="BES Unit 1 & 2">BES Unit 1 & 2</option>
<option value="Palanyag Gym, R. Medina Chapel">Palanyag Gym, R. Medina Chapel</option>
<option value="Don Bosco Covered Court">Don Bosco Covered Court</option>
<option value="Belvedere covered court">Belvedere covered court</option>
<option value="DON GALO SPORTS COMPLEX">DON GALO SPORTS COMPLEX</option>
 
<option value="Baclaran Elementary School Unit 2">Baclaran Elementary School Unit 2</option>
 
<option value="Merville Health Center">Merville Health Center </option>
<option value="Multinational Covered Court, San Agustin Covered Court, San Agustin Elementary School">Multinational Covered Court, San Agustin Covered Court,</option>
<option value="San Agustin Elementary School"> San Agustin Elementary School</option>
<option value="GOLEZEUM (CULDESAC, STA ANA, SUN VALLEY SUBDIVION)">GOLEZEUM (CULDESAC, STA ANA, SUN VALLEY SUBDIVION)</option>
<option value="San Agustin Covered Court, Multinational Village Clubhouse, San Agustin Elem school">San Agustin Covered Court, Multinational Village Clubhouse, San Agustin Elem school</option>
<option value="Tambo Elementary School / Tambo National HighSchool">Tambo Elementary School / Tambo National HighSchool</option>
<option value="Baclaran Elem. School Unit 2">Baclaran Elem. School Unit 2</option>
<option value="Tambo Elementary School / Tambo National HighSchool">tambo national school/ tambo main elem school</option>
<option value="San Dionisio Village UPS V, FC Santos Compound, Silverio Elementary School">San Dionisio Village UPS V, FC Santos Compound, Silverio Elementary School</option>
<option value="">N/A</option>
 
 

<option value="Sto Nino Health Center">Sto Nino Health Center</option>
<option value="Phase 3 Gym, Gov. Santos Day Care">Phase 3 Gym, Gov. Santos Day Care</option>
<option value="None">None</option>
<option value="Ina Homes Covered Court">Ina Homes Covered Court</option>
<option value="FC SANTOS COMPOUND">FC SANTOS COMPOUND </option>
<option value="SAN DIONISIO VILLAGE UPS 5">SAN DIONISIO VILLAGE UPS 5</option>
<option value="SILVERIO ELEMENTARY SCHOOL">SILVERIO ELEMENTARY SCHOOL</option>
<option value="SITIO FATIMA SAV 6 COVERED COURT">SITIO FATIMA SAV 6 COVERED COURT</option>
<option value="San Antonio Health Center">San Antonio Health Center</option>

   </datalist>
    <label class="active">Site of Evacuation Center</label>
  </div>

    <div class="  col s12">
<h5 style="font-size:14.4px;font-weight:500; padding-top:15px; padding-bottom:15px; padding-left:10px;background:#00658e;" class="  white-text">No. of Population in Displacement</h5>
<!-- Add fields below -->
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="evac_individuals"  >
    <label class="active">No. of Individuals</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="evac_families"  >
    <label class="active">No. of Families</label>
  </div>
 <div class="input-field col m6 s12 ">
   <input type="text" name="evac_male"  >
    <label class="active">No. of Male</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="evac_female"  >
    <label class="active">No. of Female</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="evac_pregnant"  >
    <label class="active">No. of Pregnant</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="evac_pwd"  >
    <label class="active">No. of PWD</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="evac_under_5"  >
    <label class="active">No. of < 5yo</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="evac_5_to_17"  >
    <label class="active">No. of 5-17yo</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="evac_18_to_59"  >
    <label class="active">No. of 18-59yo</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="evac_60_above"  >
    <label class="active">No. of ≥60yo</label>
  </div>

  </div>
   <div class="  col white s10 offset-s1" style="margin-top:10px;">
  <div class="  col s12">
<h5 style="font-size:14.4px;font-weight:500; padding-top:15px; padding-bottom:15px; padding-left:10px;background:#00658e;" class="  white-text">
  C. Health Consequences (Report cumulative number of casualties from the time the event occurred until the date of this report)
</h5>
<!-- Add fields below -->
  </div>
  <div class="input-field col m3 s12">
    <input type="text" name="no_of_casualties"  >
     <label class="active">Total No. of Casualties</label>
   </div>
 <div class="input-field col m3 s12">
   <input type="text" name="no_of_treated_on_site"  >
    <label class="active">No. of Treated on site</label>
  </div>
 <div class="input-field col m3 s12">
   <input type="text" name="no_of_brought_to_hospital"  >
    <label class="active">No. of Brought to hospital</label>
  </div>
 <div class="input-field col m3 s12">
   <input type="text" name="no_of_deaths"  >
    <label class="active">No. of Deaths</label>
  </div>
 <div class="input-field col s12">
   <input type="text" name="no_of_missing"  >
    <label class="active">No. of Missing</label>
  </div>
   <div class="input-field col s7 teal lighten-5" style="padding-top:4px;padding-bottom:4px;">
<small>Attachments to this Report:    Separate List of Causalities (if needed)</small>
  </div>
  </div>


   <div class="  col white s10 offset-s1" style="margin-top:10px;">
    <div class="  col s12">
<h5 style="font-size:14.4px;font-weight:500; padding-top:15px; padding-bottom:15px; padding-left:10px;background:#00658e;" class="  white-text">
    D. Morbidity Cases (Report only the NEW cases from the date of last report)
</h5>
<!-- Add fields below -->
  </div>

 <div class="input-field col m6 s12">
   <input type="text" name="cause_1_d"  >
    <label class="active">CAUSES 1 (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_zero_to_fifteen_d"  >
    <label class="active">(C1)  NO. OF 0-15 yrs  (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_greater_than_fifteen_d"  >
    <label class="active">(C1)  NO. OF >15 yrs  (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="total_cause_1_d"  >
    <label class="active">Total (Causes 1)  (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="cause_2_d"  >
    <label class="active">CAUSES 2  (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_zero_to_fifteen_d_c2"  >
    <label class="active">(C2)  NO. OF 0-15 yrs  (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_greater_than_fifteen_d_c2"  >
    <label class="active">(C2)  NO. OF >15 yrs  (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="total_cause_2_d"  >
    <label class="active">Total (Causes 2)  (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="cause_3_d"  >
    <label class="active">CAUSES 3  (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_zero_to_fifteen_d_c3"  >
    <label class="active">(C3)  NO. OF 0-15 yrs  (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_greater_than_fifteen_d_c3"  >
    <label class="active">(C3)  NO. OF >15 yrs  (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="total_cause_3_d"  >
    <label class="active">Total (Causes 3)  (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="cause_4_d"  >
    <label class="active">CAUSES 4  (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_zero_to_fifteen_d_c4"  >
    <label class="active">(C4)  NO. OF 0-15 yrs  (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_greater_than_fifteen_d_c4"  >
    <label class="active">(C4)  NO. OF >15 yrs  (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="total_cause_4_d"  >
    <label class="active">Total (Causes 4) (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="cause_5_d"  >
    <label class="active">CAUSES 5  (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_zero_to_fifteen_d_c5"  >
    <label class="active">(C5)  NO. OF 0-15 yrs  (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_greater_than_fifteen_d_c5"  >
    <label class="active">(C5)  NO. OF >15 yrs  (IEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="total_cause_5_d"  >
    <label class="active">Total (Causes 5) (IEC)</label>
  </div>
  </div>


   <div class="  col white s10 m10 offset-m1 offset-s1" style="margin-top:10px;">
   <div class="  col s12">
  <h5 style="font-size:14.4px;font-weight:500; padding-top:15px; padding-bottom:15px; padding-left:10px;background:#00658e;" class="  white-text">
   TOP FIVE LEADING CAUSES OF CONSULTATION OUTSIDE EVACUATION CENTERS
</h5>
    </div>
 <div class="input-field col m6 s12">
   <input type="text" name="causes_1_top_five"  >
    <label class="active">Causes 1 (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_zero_to_fifteen_d_c1_top_five"  >
    <label class="active">(C1)  NO. OF 0-15 yrs  (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_greater_than_fifteen_d_c1_top_five"  >
    <label class="active">(C1)  NO. OF  >15 yrs (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="total_cause_1_top_five_top_five"  >
    <label class="active">Total (Causes 1) (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="causes_2_top_five"  >
    <label class="active">Causes 2 (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_zero_to_fifteen_d_c2_top_five"  >
    <label class="active">(C2)  NO. OF 0-15 yrs  (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_greater_than_fifteen_d_c2_top_five"  >
    <label class="active">(C2)  NO. OF  >15 yrs (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="total_cause_2_top_five"  >
    <label class="active">Total (Causes 2) (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="causes_3_top_five"  >
    <label class="active">Causes 3 (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_zero_to_fifteen_d_c3_top_five"  >
    <label class="active">(C3)  NO. OF 0-15 yrs  (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_greater_than_fifteen_d_c3_top_five"  >
    <label class="active">(C3)  NO. OF  >15 yrs (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="total_cause_3_top_five"  >
    <label class="active">Total (Causes 3) (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="causes_4_top_five"  >
    <label class="active">Causes 4 (OEC)</label>
  </div>

 <div class="input-field col m6 s12">
<!-- LAST -->
   <input type="text" name="no_of_zero_to_fifteen_d_c4_top_five"  >
    <label class="active">(C4)  NO. OF 0-15 yrs  (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_greater_than_fifteen_d_c4_top_five"  >
    <label class="active">(C4)  NO. OF  >15 yrs (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="total_cause_4_top_five"  >
    <label class="active">Total (Causes 4) (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="causes_5_top_five"  >
    <label class="active">Causes 5 (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_zero_to_fifteen_d_c5_top_five"  >
    <label class="active">(C5)  NO. OF 0-15 yrs  (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="no_of_greater_than_fifteen_d_c5_top_five"  >
    <label class="active">(C5)  NO. OF  >15 yrs (OEC)</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="total_cause_5_top_five"  >
    <label class="active">Total (Causes 5) (OEC)</label>
  </div>
  </div>


   <div class="  col white s10 offset-s1" style="margin-top:10px;">
    <div class="input-field col s12">
  <h5 style="font-size:14.4px;font-weight:500; padding-top:15px; padding-bottom:15px; padding-left:10px;background:#00658e;" class="  white-text">
  E. Services Provided
</h5>
 <div class="input-field col m3 s12">
   <input type="text" name="service_1_services_provided"  >
    <label class="active">Service 1</label>
  </div>
 <div class="input-field col m3 s12">
   <input type="text" name="service_2_services_provided"  >
    <label class="active">Service 2</label>
  </div>
 <div class="input-field col m3 s12">
   <input type="text" name="service_3_services_provided"  >
    <label class="active">Service 3</label>
  </div>
 <div class="input-field col m3 s12">
   <input type="text" name="service_4_services_provided"  >
    <label class="active">Service 4</label>
  </div>
 <div class="input-field col s12">
   <input type="text" name="service_5_services_provided"  >
    <label class="active">Service 5</label>
  </div>
  </div>
  </div>

   <div class="  col white s10 offset-s1" style="margin-top:10px;">
      <div class="input-field col s12">
  <h5 style="font-size:14.4px;font-weight:500; padding-top:15px; padding-bottom:15px; padding-left:10px;background:#00658e;" class="  white-text">
F. Status of Essential Drugs and Supplies (Report only the NEW data from the date of last report)
</h5>
 <div class="input-field col s4">
   <input type="text" name="essential_drug_1"  >
    <label class="active">Essential Drug 1</label>
  </div>
 <div class="input-field col s4">
   <input type="text" name="stock_quantity_ed1"  >
    <label class="active">(ED1) Stock Quantity</label>
  </div>
 <div class="input-field col s4">
   <input type="text" name="stock_quantity_remaining_ed1"  >
    <label class="active">(ED1)  Stock quantity remaining</label>
  </div>
 <div class="input-field col s4">
   <input type="text" name="essential_drug_2"  >
    <label class="active">Essential Drug 2</label>
  </div>
 <div class="input-field col s4">
   <input type="text" name="quantity_given_ed2"  >
    <label class="active">(ED2) Quantity Given</label>
  </div>
 <div class="input-field col s4">
   <input type="text" name="stock_quantity_remaining_ed2"  >
    <label class="active">(ED2)  Stock quantity remaining</label>
  </div>
 <div class="input-field col s4">
   <input type="text" name="essential_drug_3"  >
    <label class="active">Essential Drug 3</label>
  </div>
 <div class="input-field col s4">
   <input type="text" name="quantity_given_ed3"  >
    <label class="active">(ED3) Quantity Given</label>
  </div>
 <div class="input-field col s4">
   <input type="text" name="stock_quantity_remaining_ed3"  >
    <label class="active">(ED3)  Stock quantity remaining</label>
  </div>
 <div class="input-field col s4">
   <input type="text" name="essential_drug_4"  >
    <label class="active">Essential Drug 4</label>
  </div>
 <div class="input-field col s4">
   <input type="text" name="quantity_given_ed4"  >
    <label class="active">(ED4) Quantity Given</label>
  </div>
 <div class="input-field col s4">
   <input type="text" name="stock_quantity_remaining_ed4"  >
    <label class="active">(ED4)  Stock quantity remaining</label>
  </div>
 <div class="input-field col s4">
   <input type="text" name="essential_drug_5"  >
    <label class="active">Essential Drug 5</label>
  </div>
 <div class="input-field col s4">
   <input type="text" name="quantity_given_ed5"  >
    <label class="active">(ED5) Quantity Given</label>
  </div>
 <div class="input-field col s4">
   <input type="text" name="stock_quantity_remaining_ed5"  >
    <label class="active">(ED5)  Stock quantity remaining</label>
  </div>
  </div>
  </div>

   <div class="  col white s10 offset-s1" style="margin-top:10px;">
     <div class="input-field col s12" >
  <h5 style="font-size:14.4px;font-weight:500; padding-top:15px; padding-bottom:15px; padding-left:10px;background:#00658e;" class="  white-text">
G. Lifelines in the Affected Areas
</h5>
  </div>
<p class=" " style="padding:10px;">
  <span class="white-text blue-grey darken-4 col s12 m12" style="padding-top:5px;padding-bottom:5px;">Communication</span> 
     &nbsp&nbsp   <label style="" style="padding-top:5px;">
        <input name="communication" value="Fully Functional" type="checkbox"   />
        <span>Fully Functional     </span>
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
      <label>
        <input name="communication" value="Partly Functional" type="checkbox"   />
        <span>Partly Functional   </span>
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
      <label>
        <input name="communication" value="Totally Non-Functional" type="checkbox"   />
        <span>Totally Non-Functional   </span>
      </label>
 <label><input style="margin-left:0.7em;" placeholder="Remarks" type="text" name="remarks_communication" id=""></label>
    </p>
<p class=" " style="padding:10px;">
  <span class="white-text blue-grey darken-4 col s12" style="padding-top:5px;padding-bottom:5px;">Electric Power</span> <br><br>
  &nbsp&nbsp      <label>
        <input  name="electric_power" value="Fully Functional" type="checkbox"   />
        <span>Fully Functional     </span>
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
      <label>
        <input style="margin-left:0.7em;" name="electric_power" value="Partly Functional" type="checkbox"   />
        <span>Partly Functional   </span>
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
      <label>
        <input style="margin-left:0.7em;" name="electric_power" value="Totally Non-Functional" type="checkbox"   />
        <span>Totally Non-Functional   </span>
      </label>
 <label><input style="margin-left:0.7em;" placeholder="Remarks" type="text" name="remarks_electric_power" id=""></label>
    </p>
<p class=" " style="padding:10px;">
  <span class="white-text blue-grey darken-4 col s12" style="padding-top:5px;padding-bottom:5px;">Water</span> <br><br>
    &nbsp&nbsp    <label>
        <input style="margin-left:0.7em;" name="water" value="Fully Functional" type="checkbox"   />
        <span>Fully Functional     </span>
         
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
      <label>
        <input style="margin-left:0.7em;" name="water"  value="Partly Functional" type="checkbox"   />
        <span>Partly Functional   </span>
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
      <label>
        <input name="water" value="Totally Non-Functional" type="checkbox"   />
        <span>Totally Non-Functional   </span>
      </label>
 <label><input style="margin-left:0.7em;" placeholder="Remarks" type="text" name="remarks_water" id=""></label>
    </p>
<p class=" " style="padding:7px;">
  <span class="white-text blue-grey darken-4 col s12" style="padding-top:5px;padding-bottom:5px;">Roads/Bridges</span> <br><br>
    &nbsp&nbsp    <label>
        <input style="margin-left:0.7em;" name="roads_bridges" value="Fully Functional" type="checkbox"   />
        <span>Fully Functional     </span>
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
      <label>
        <input name="roads_bridges" value="Partly Functional" type="checkbox"   />
        <span>Partly Functional   </span>
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
      <label>
        <input name="roads_bridges" value="Totally Non-Functional" type="checkbox"   />
        <span>Totally Non-Functional   </span>
      </label>
 <label><input style="margin-left:0.7em;" placeholder="Remarks" type="text" name="remarks_road_bridges" id=""></label>
    </p>
<p class=" " style="padding:7px;">
  <span class="white-text blue-grey darken-4 col s12" style="padding-top:5px;padding-bottom:5px;">Other</span> <br><br>
    &nbsp&nbsp  <label>
        <input name="Other" value="Fully Functional" type="checkbox"   />
        <span>Fully Functional     </span>
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
      <label>
       
        <input name="Other" value="Partly Functional" type="checkbox"   />
        <span>Partly Functional   </span>
      </label>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
      
      <label>
        <input name="Other" value="Totally Non-Functional" type="checkbox"   />
        <span>Totally Non-Functional  </span>
      </label>
<label><input style="margin-left:0.7em;" placeholder="Remarks" type="text" name="remarks_other" id=""></label>
    </p>
  </div>

   <div class="  col white s10 offset-s1" style="margin-top:10px;">
 <div class="input-field col s12">
<h5 style="font-size:14.4px;font-weight:500; padding-top:15px; padding-bottom:15px; padding-left:10px;background:#00658e;" class="  white-text">
H. Problems Encountered 
</h5>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="problems_encountered"  >
    <label class="active">Problems Encountered</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="recommendations"  >
    <label class="active">Recommendations</label>
  </div>
 <div class="input-field col m3 s12">
   <input type="text" name="quantity_given_ed1"  >
    <label class="active">(ED1) Quantity Given</label>
  </div>
 <div class="input-field col m3 s12">
   <input type="text" name="stock_quantity_ed2"  >
    <label class="active">(ED2) Stock Quantity</label>
  </div>
 <div class="input-field col m3 s12">
   <input type="text" name="stock_quantity_ed3"  >
    <label class="active">(ED3) Stock Quantity</label>
  </div>
 <div class="input-field col m3 s12">
   <input type="text" name="stock_quantity_ed4"  >
    <label class="active">(ED4) Stock Quantity</label>
  </div>
 <div class="input-field col s12">
   <input type="text" name="stock_quantity_ed5"  >
    <label class="active">(ED5) Stock Quantity</label>
  </div>
 
 <div class="input-field col m3 s12">
   <input type="text" name="service_1_no_of_patient"  >
    <label class="active">Service 1 - Number of Patient</label>
  </div>
 <div class="input-field col m3 s12">
   <input type="text" name="service_2_no_of_patient"  >
    <label class="active">Service 2 - Number of Patient</label>
  </div>
 <div class="input-field col m3 s12">
   <input type="text" name="service_3_no_of_patient"  >
    <label class="active">Service 3 - Number of Patient</label>
  </div>
 <div class="input-field col m3 s12">
   <input type="text" name="service_4_no_of_patient"  >
    <label class="active">Service 4 - Number of Patient</label>
  </div>
 <div class="input-field col s12">
   <input type="text" name="service_5_no_of_patient"  >
    <label class="active">Service 5 - Number of Patient</label>
  </div>
  </div>





   <div class="  col white s10 offset-s1" style="margin-top:10px;">
 <div class="input-field col s12">
<h5 style="font-size:14.4px;font-weight:500; padding-top:15px; padding-bottom:15px; padding-left:10px;background:#00658e;" class="  white-text">
  Prepared and submitted by:
  </h5>
  </div>
 <div class="input-field col m6 s12">
   <input type="date" name="date_prepared"  >
    <label class="active">Date Prepared:</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="mobile_no"  >
    <label class="active">Mobile No.:</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="printed_name"  >
    <label class="active">Printed Name:</label>
  </div>
 <div class="input-field col m6 s12">
   <input type="text" name="email" value="<?php echo $_SESSION['email']; ?>"  >
    <label class="active">Email:</label>
  </div>
 <div class="input-field col s12">
   <input type="text" name="designation_office"  >
    <label class="active">Designation/Office:</label>
  </div>
   <div class="input-field col s12">
  <button class="btn blue darken-2 btn-small" type="submit" style="font-weight:600 !important;">Submit Report</button>
    </div>
  </div>




 

</form>

<div id="result" class="section"></div>
<!-- jQuery -->
 
<script>
document.addEventListener('DOMContentLoaded', function () {
  // Initialize Materialize selects
  const elems = document.querySelectorAll('select');
  M.FormSelect.init(elems);
});

document.getElementById('evacuationForm').addEventListener('submit', function (e) {
  e.preventDefault();

  const form = this;
  const requiredFields = form.querySelectorAll('[required]');
  let allFilled = true;

  // Validate each required field
  requiredFields.forEach(field => {
    const value = field.value.trim();

    if (!value || value === '') {
      allFilled = false;
      field.classList.add('invalid');

      // If it's a select (Materialize), manually set red border
      if (field.tagName === 'SELECT') {
        const instance = M.FormSelect.getInstance(field);
        const selectWrapper = field.closest('.select-wrapper');
        if (selectWrapper) {
          selectWrapper.querySelector('input.select-dropdown').classList.add('invalid');
        }
      }
    } else {
      field.classList.remove('invalid');

      // Remove red border if corrected
      if (field.tagName === 'SELECT') {
        const selectWrapper = field.closest('.select-wrapper');
        if (selectWrapper) {
          selectWrapper.querySelector('input.select-dropdown').classList.remove('invalid');
        }
      }
    }
  });

  // Show toast if not all required fields are filled
  if (!allFilled) {
    M.toast({ html: 'Please fill out all required fields.', classes: 'red' });
    return;
  }

  // FormData & fetch
  const formData = new FormData(form);

  fetch('save_evacuation_report.php', {
    method: 'POST',
    body: formData
  })
    .then(res => res.text())
    .then(data => {
      if (data.includes('successfully')) {
        M.toast({ html: 'Record successfully added!', classes: 'green' });
        form.reset();
        M.updateTextFields();

        // Re-initialize Materialize selects after reset
        const elems = document.querySelectorAll('select');
        M.FormSelect.init(elems);
      } else {
        M.toast({ html: data || 'Error saving record', classes: 'red' });
      }
    })
    .catch(err => {
      console.error(err);
      M.toast({ html: 'An error occurred.', classes: 'red' });
    });
});
</script>





<?php include 'footer.php'; ?>
