 
<?php
require 'db_connect.php';
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

$barangay = $_SESSION['barangay'];
 
?>

<!-- Materialize & DataTables Includes -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>

<style>
  .dataTables_length { display:none !important; }
    table.dataTable tbody td {
   padding: 0 0 0 19px !important;
  height: 48px !important;
  font-size: 12px !important;
  font-family: system-ui;
  color: #37474f !important;
  border-bottom: solid 1px #cfd8dc !important;
  white-space: nowrap !important;
  font-weight:500;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  }
 
  th {
      border-bottom: solid 1px #cfd8dc !important;
    font-size: 12px;
    font-weight: 500;
    color: #1a237e !important;
    cursor: pointer;
    white-space: nowrap;

    padding: 0;
    height: 55px;
    padding-left: 18px !important;
    vertical-align: middle !important;
    outline: none !important;
  }
 .dataTables_wrapper .dataTables_paginate .paginate_button {
  color: #1a237e !important; /* Indigo text */
  padding: 0.4rem 1rem;
  margin: 0 2px;
  border-radius: 4px;
  background-color: #e3f2fd; /* Light blue background */
  border: none;
  transition: background-color 0.2s ease, color 0.2s ease;
  font-weight: 500;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background-color: #bbdefb; /* Slightly darker on hover */
  color: #0d47a1 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
  background-color: dodgerblue; /* Indigo background */
  color: white !important;
  font-weight: bold;
  box-shadow: none !important;
}

.dataTables_wrapper .dataTables_paginate {
  margin-top: 1rem;
  text-align: center;
}

  table.dataTable.stripe > tbody > tr.odd { background-color: #f1f8e9; }
  table.dataTable.hover > tbody > tr:hover { background-color: #e3f2fd; }
 
  .loading { text-align: center; padding: 20px; }
</style>
<?php include 'header.php'; ?>
<div class="row ">
 
<nav class="blue darken-2 z-depth-0">
  <span style="margin-left:12.9px; font-size:17.6px;">Records</span>
</nav>
<br>
<div class="col s12 m12 " style="padding:16px;">
  <div class="input-field white" style="border:1px solid #ddd; padding:0px; margin:0px; border-radius:2px;">
   
     

    <div style="padding-top:20px;padding-bottom:20px;padding-left:8px;">
      <form id="filterForm">
        
        
        <div class="input-field col m4 s12">
          <input id="date_from" name="date_from" type="date" class="validate">
          <label for="date_from" class="active">Date From</label>
        </div>
        
         <div class="input-field col m4 s12">
          <input id="date_to" name="date_to" type="date" class="validate">
          <label for="date_to" class="active">Date To</label>
        </div>
        
     <div class="input-field col m3 s12">
        
          
          <input placeholder="Barangay" list="brgy" id="barangay_filter" name="barangay">

<datalist id="brgy">
  <option value="">All Barangays</option>
     <option value="BACLARAN">Baclaran</option>
            <option value="BF HOMES">BF Homes</option>
            <option value="DON BOSCO">Don Bosco</option>
            <option value="DON GALO">Don Galo</option>
            <option value="LA HUERTA">La Huerta</option>
            <option value="MARCELO GREEN">Marcelo Green</option>
            <option value="MERVILLE">Merville</option>
            <option value="MOONWALK">Moonwalk</option>
            <option value="SAN ANTONIO">San Antonio</option>
            <option value="SAN DIONISIO">San Dionisio</option>
            <option value="SAN ISIDRO">San Isidro</option>
            <option value="SAN MARTIN DE PORRES">San Martin de Porres</option>
            <option value="SANTO NIÑO">Santo Niño</option>
            <option value="SUN VALLEY">Sun Valley</option>
            <option value="TAMBO">Tambo</option>
            <option value="VITALEZ">Vitalez</option>
</datalist>



        </div>
        <br>
        <button class="btn   btn-small" style="background:#007b8b;" type="submit">Search</button>
      
      </form>
    </div>
  </div>
</div>

<div class="col s12 m12" style="padding:17px;margin:00px;"> 
 
 
  <table id="evacTable" class=" white" style="margin-top:0px;border:1px solid #ddd !important;">
    <thead class="   " style="background:#;">
      <tr >
        <th style="border-radius:0 !important;">#</th>
        <th style="border-radius:0 !important;">Date</th>
        <th style="border-radius:0 !important;">Time</th>
        <th style="border-radius:0 !important;">Barangay</th>
        <th style="border-radius:0 !important;">Geologic</th>
        <th style="border-radius:0 !important;">Weather</th>
        <th style="border-radius:0 !important;">Manmade</th>
        <th style="border-radius:0 !important;">Actions</th>
      </tr>
    </thead>
    <tbody id="recordsTableBody">
      <tr class="loading">
        <td colspan="8">
          <div class="preloader-wrapper small active">
            <div class="spinner-layer spinner-blue-only">
              <div class="circle-clipper left">
                <div class="circle"></div>
              </div>
              <div class="gap-patch">
                <div class="circle"></div>
              </div>
              <div class="circle-clipper right">
                <div class="circle"></div>
              </div>
            </div>
          </div>
          Loading records...
        </td>
      </tr>
    </tbody>
  </table><br>
  <a href="sheetjs.php" class="btn btn-small green darken-3" style="font-weight:600;font-size:12px;"><i class="material-icons left" style="font-weight:bold;font-size:20px;">file_download</i>Export Data to Excel</a>
  <a href="add_health_center_report.php" class="btn btn-small light-blue  darken-1 " style="font-weight:600;font-size:12px;"> <i class="material-icons left" style="font-weight:bold;font-size:20px;">add</i>Add New Record</a>
</div>

</div>

<script>
$(document).ready(function() {
  // Initialize Materialize components
  M.FormSelect.init(document.querySelectorAll('select'));
  
  let dataTable;
  let currentRecords = [];
  
  // Load initial data
  loadRecords();
  
  // Filter form submission
  $('#filterForm').on('submit', function(e) {
    e.preventDefault();
    loadRecords();
  });
  
  // Reset filters
  $('#resetBtn').on('click', function() {
    $('#filterForm')[0].reset();
    M.FormSelect.init(document.querySelectorAll('select'));
    loadRecords();
  });
  
  // Export functionality
  $('#exportBtn').on('click', function() {
    exportToCSV();
  });
  
  function loadRecords() {
    const formData = new FormData(document.getElementById('filterForm'));
    
    // Debug: Log form data
    for (let [key, value] of formData.entries()) {
      console.log(key, value);
    }
    
    // Show loading
    $('#recordsTableBody').html(`
      <tr class="loading">
        <td colspan="8" class="center-align">
          <div class="preloader-wrapper small active">
            <div class="spinner-layer spinner-blue-only">
              <div class="circle-clipper left"><div class="circle"></div></div>
              <div class="gap-patch"><div class="circle"></div></div>
              <div class="circle-clipper right"><div class="circle"></div></div>
            </div>
          </div>
          Loading records...
        </td>
      </tr>
    `);
    
    // Destroy existing DataTable
    if (dataTable) {
      dataTable.destroy();
      dataTable = null;
    }
    
    fetch('fetch_records.php', {
      method: 'POST',
      body: formData
    })
    .then(response => {
      console.log('Response status:', response.status);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.text();
    })
    .then(text => {
      console.log('Raw response:', text);
      try {
        const data = JSON.parse(text);
        console.log('Parsed data:', data);
        
        if (data.success) {
          currentRecords = data.records;
          displayRecords(data.records);
          M.toast({html: `Found ${data.count} records`, classes: 'green'});
        } else {
          $('#recordsTableBody').html(`
            <tr><td colspan="8" class="center-align red-text">${data.message}</td></tr>
          `);
          M.toast({html: data.message, classes: 'red'});
        }
      } catch (e) {
        console.error('JSON parse error:', e);
        $('#recordsTableBody').html(`
          <tr><td colspan="8" class="center-align red-text">Invalid response format</td></tr>
        `);
        M.toast({html: 'Invalid response format', classes: 'red'});
      }
    })
    .catch(error => {
      console.error('Fetch error:', error);
      $('#recordsTableBody').html(`
        <tr><td colspan="8" class="center-align red-text">Error loading records: ${error.message}</td></tr>
      `);
      M.toast({html: 'Error loading records', classes: 'red'});
    });
  }
  
  function displayRecords(records) {
    let html = '';
    
    if (records.length === 0) {
      html = '<tr><td colspan="8" class="center-align">No records found</td></tr>';
    } else {
      records.forEach((record, index) => {
        html += `
          <tr>
            <td>${record.id || '-'}</td>
            <td>${formatDate(record.activity_date)}</td>
            <td>${formatTime(record.activity_time)}</td>
            <td>${record.barangay || '-'}</td>
            <td>${record.GEOLOGIC || '-'}</td>
            <td>${record.WEATHER || '-'}</td>
            <td>${record.MANMADE || '-'}</td>
            
            <td>
              <a href="#" class="btn-small indigo darken-4 view-btn" data-id="${record.id}" title="View Record">
                <i class="material-icons">visibility</i>
              </a>
              <a href="#" class="btn-small red darken-4 delete-btn" data-id="${record.id}" title="Delete Record">
                <i class="material-icons">delete</i>
              </a>
            </td>
          </tr>
        `;
      });
    }
    
    $('#recordsTableBody').html(html);
    
    // Initialize DataTable only if we have records
    if (records.length > 0) {
      try {
        dataTable = $('#evacTable').DataTable({
          pageLength: 8,
          responsive: true,
          order: [[0, 'desc']],
          searching: false,
          lengthChange: true,
          info: true,
          destroy: true // Allow reinitialization
        });
      } catch (e) {
        console.error('DataTable initialization error:', e);
      }
    }
  }
  
  function formatDate(dateString) {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString; // Return original if invalid
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (e) {
      return dateString;
    }
  }
  
  function formatTime(timeString) {
    if (!timeString) return '-';
    try {
      const time = new Date('1970-01-01T' + timeString + 'Z');
      if (isNaN(time.getTime())) return timeString; // Return original if invalid
      return time.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    } catch (e) {
      return timeString;
    }
  }
  
  function exportToCSV() {
    if (currentRecords.length === 0) {
      M.toast({html: 'No records to export', classes: 'orange'});
      return;
    }
    
    try {
      const headers = ['ID', 'Date', 'Time', 'Barangay', 'Geologic', 'Weather', 'Manmade'];
      const csvContent = [
        headers.join(','),
        ...currentRecords.map(record => [
          record.id || '',
          record.activity_date || '',
          record.activity_time || '',
          record.barangay || '',
          record.GEOLOGIC || '',
          record.WEATHER || '',
          record.MANMADE || ''
        ].map(field => `"${field}"`).join(','))
      ].join('\n');
      
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `evacuation_records_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      
      M.toast({html: 'Records exported successfully', classes: 'green'});
    } catch (e) {
      console.error('Export error:', e);
      M.toast({html: 'Error exporting records', classes: 'red'});
    }
  }
  
  // Handle view and delete actions
  $(document).on('click', '.view-btn', function(e) {
    e.preventDefault();
    const id = $(this).data('id');
    if (id) {
      viewRecord(id);
    }
  });

  function viewRecord(id) {
    // Initialize modal
    const modal = M.Modal.init(document.getElementById('viewModal'));
    
    // Show loading in modal
    $('#modalContent').html(`
      <div class="center-align">
        <div class="preloader-wrapper small active">
          <div class="spinner-layer spinner-blue-only">
            <div class="circle-clipper left"><div class="circle"></div></div>
            <div class="gap-patch"><div class="circle"></div></div>
            <div class="circle-clipper right"><div class="circle"></div></div>
          </div>
        </div>
        <p>Loading record details...</p>
      </div>
    `);
    
    // Open modal
    modal.open();
    
    // Fetch record details
    fetch('view_record.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({id: id})
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      if (data.success) {
        displayRecordDetails(data.record);
      } else {
        $('#modalContent').html(`
          <div class="center-align red-text">
            <i class="material-icons">error</i>
            <p>${data.message}</p>
          </div>
        `);
      }
    })
    .catch(error => {
      console.error('View error:', error);
      $('#modalContent').html(`
        <div class="center-align red-text">
          <i class="material-icons">error</i>
          <p>Error loading record details</p>
        </div>
      `);
    });
  }

  function displayRecordDetails(record) {
    const html = `
      <div class="row">
        <div class="col s12">
          <table class="striped">
            <tbody>
              <tr>
                <td><strong>Record ID:</strong></td>
                <td>${record.id || '-'}</td>
              </tr>
              <tr>
                <td><strong>Date:</strong></td>
                <td>${formatDate(record.activity_date)}</td>
              </tr>
              <tr>
                <td><strong>Time:</strong></td>
                <td>${formatTime(record.activity_time)}</td>
              </tr>
              <tr>
                <td><strong>Barangay:</strong></td>
                <td>${record.barangay || '-'}</td>
              </tr>
              <tr>
                <td><strong>Geologic Events:</strong></td>
                <td>${record.GEOLOGIC || '-'}</td>
              </tr>
              <tr>
                <td><strong>Weather Events:</strong></td>
                <td>${record.WEATHER || '-'}</td>
              </tr>
              <tr>
                <td><strong>Man-made Events:</strong></td>
                <td>${record.MANMADE || '-'}</td>
              </tr>
              <tr>
                <td><strong>Affected Areas:</strong></td>
                <td>${record.affected_areas || '-'}</td>
              </tr>
              <tr>
                <td><strong>Individuals Affected:</strong></td>
                <td>${record.individuals_affected || '-'}</td>
              </tr>
              <tr>
                <td><strong>Families Affected:</strong></td>
                <td>${record.families_affected || '-'}</td>
              </tr>
              <tr>
                <td><strong>Evacuation Center:</strong></td>
                <td>${record.evacuation_site || '-'}</td>
              </tr>
              <tr>
                <td><strong>Number of < 5yo</strong></td>
                <td>${record.evac_under_5 || '-'}</td>
              </tr>
              <tr>
                <td><strong>Number  of 5-17yo</strong></td>
                <td>${record.evac_5_to_17 || '-'}</td>
              </tr>
              <tr>
                <td><strong>Number of 18-59 years old</strong></td>
                <td>${record.evac_18_to_59 || '-'}</td>
              </tr>
              <tr>
                <td><strong>Number  of ≥60 years old</strong></td>
                <td>${record.evac_60_above || '-'}</td>
              </tr>
 
          
       
         
            </tbody>
          </table>
        </div>
      </div>
    `;
    
    $('#modalContent').html(html);
  }
  
  $(document).on('click', '.delete-btn', function(e) {
    e.preventDefault();
    const id = $(this).data('id');
    
    if (id && confirm('Are you sure you want to delete this record?')) {
      deleteRecord(id);
    }
  });
  
  function deleteRecord(id) {
    fetch('delete_record.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({id: id})
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      if (data.success) {
        M.toast({html: 'Record deleted successfully', classes: 'green'});
        loadRecords(); // Reload records
      } else {
        M.toast({html: data.message || 'Failed to delete record', classes: 'red'});
      }
    })
    .catch(error => {
      console.error('Delete error:', error);
      M.toast({html: 'Error deleting record', classes: 'red'});
    });
  }
});
</script>

<!-- Modal for viewing records -->
<div id="viewModal" class="modal">
  <div class="modal-content" id="modalContent">
    <!-- Record details will be loaded here -->
  </div>
  <div class="modal-footer">
    <a href="#!" class="modal-close waves-effect waves-green btn-flat">Close</a>
  </div>
</div>

 
